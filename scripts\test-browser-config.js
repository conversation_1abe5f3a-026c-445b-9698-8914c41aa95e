/**
 * 测试浏览器环境下配置获取的脚本
 * 验证去除本地 config.json 引用后，浏览器环境是否能正常通过 3031 端口获取配置
 */

const fetch = require('node-fetch')

// 测试配置
const BASE_URL = 'http://localhost:3031'

// 模拟浏览器环境
global.window = {
  electron: undefined,
  api: undefined
}

// 测试主进程配置端点
async function testMainProcessConfigEndpoint() {
  console.log('🔍 测试主进程配置端点...\n')
  
  try {
    console.log('📋 测试 GET /api/config/get')
    const response = await fetch(`${BASE_URL}/api/config/get`)
    
    if (response.ok) {
      const config = await response.json()
      console.log('   ✅ 成功获取配置')
      console.log('   配置结构:', Object.keys(config))
      
      if (config.APP) {
        console.log('   APP 配置:', Object.keys(config.APP))
        return { success: true, config }
      } else {
        console.log('   ⚠️  配置中缺少 APP 部分')
        return { success: false, error: '配置结构不完整' }
      }
    } else {
      const error = await response.text()
      console.log(`   ❌ 请求失败: ${response.status}`)
      console.log(`   错误: ${error}`)
      return { success: false, error: `HTTP ${response.status}: ${error}` }
    }
  } catch (error) {
    console.log(`   ❌ 网络错误: ${error.message}`)
    return { success: false, error: error.message }
  }
}

// 测试 electronApi 配置获取
async function testElectronApiConfig() {
  console.log('\n🔍 测试 electronApi 配置获取...\n')
  
  try {
    // 动态导入 electronApi
    const electronApiModule = await import('../src/renderer/src/utils/electronApi.js')
    const electronApi = electronApiModule.default
    
    console.log('📋 测试 electronApi.config.getConfig()')
    const config = await electronApi.config.getConfig()
    
    if (config && config.APP) {
      console.log('   ✅ 成功获取配置')
      console.log('   配置结构:', Object.keys(config))
      console.log('   APP 配置:', Object.keys(config.APP))
      return { success: true, config }
    } else {
      console.log('   ❌ 配置获取失败或结构不正确')
      console.log('   返回值:', config)
      return { success: false, error: '配置结构不正确' }
    }
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`)
    return { success: false, error: error.message }
  }
}

// 测试设备信息获取
async function testDeviceInfo() {
  console.log('\n🔍 测试设备信息获取...\n')
  
  try {
    const electronApiModule = await import('../src/renderer/src/utils/electronApi.js')
    const electronApi = electronApiModule.default
    
    console.log('📋 测试 electronApi.device.getMachineInfo()')
    const machineInfo = await electronApi.device.getMachineInfo()
    
    if (machineInfo && machineInfo.machineId) {
      console.log('   ✅ 成功获取机器信息')
      console.log('   机器ID:', machineInfo.machineId.substring(0, 8) + '...')
      return { success: true, machineInfo }
    } else {
      console.log('   ❌ 机器信息获取失败')
      console.log('   返回值:', machineInfo)
      return { success: false, error: '机器信息获取失败' }
    }
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`)
    return { success: false, error: error.message }
  }
}

// 模拟 store/config 的 loadConfig 函数
async function testStoreConfigLoad() {
  console.log('\n🔍 测试 store/config loadConfig 逻辑...\n')
  
  try {
    const electronApiModule = await import('../src/renderer/src/utils/electronApi.js')
    const electronApi = electronApiModule.default
    
    console.log('📋 模拟 loadConfig 流程')
    
    // 获取配置
    const config = await electronApi.config.getConfig()
    console.log('   1. 获取配置:', config ? '✅ 成功' : '❌ 失败')
    
    if (config && config.APP) {
      const appConfig = config.APP
      console.log('   2. 解析 APP 配置:', '✅ 成功')
      console.log('      - name:', appConfig.name || '(空)')
      console.log('      - pwd:', appConfig.pwd ? '(已设置)' : '(未设置)')
      
      if (!appConfig.pwd) {
        // 获取机器码作为密码
        const machineInfo = await electronApi.device.getMachineInfo()
        console.log('   3. 获取机器码作为密码:', machineInfo ? '✅ 成功' : '❌ 失败')
      } else {
        console.log('   3. 使用配置中的密码:', '✅ 跳过')
      }
      
      return { success: true, appConfig }
    } else {
      console.log('   2. 配置结构错误:', '❌ 失败')
      return { success: false, error: '配置结构错误' }
    }
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`)
    return { success: false, error: error.message }
  }
}

// 主函数
async function main() {
  console.log('🚀 浏览器环境配置获取测试工具\n')
  console.log('📝 测试目标: 验证去除本地 config.json 引用后，浏览器环境能否正常获取配置\n')
  
  // 检查服务是否运行
  try {
    const response = await fetch(`${BASE_URL}/health`)
    if (!response.ok) {
      throw new Error('服务未响应')
    }
    console.log('✅ 主进程 HTTP 服务正在运行\n')
  } catch (error) {
    console.error('❌ 主进程 HTTP 服务未运行，请先启动 Electron 应用')
    console.error('   运行命令: npm run dev\n')
    process.exit(1)
  }
  
  // 运行测试
  const results = []
  
  results.push(await testMainProcessConfigEndpoint())
  results.push(await testElectronApiConfig())
  results.push(await testDeviceInfo())
  results.push(await testStoreConfigLoad())
  
  // 统计结果
  const successCount = results.filter(r => r.success).length
  const totalCount = results.length
  
  console.log('\n' + '='.repeat(60))
  console.log('📋 测试结果总结:')
  console.log(`- 通过测试: ${successCount}/${totalCount}`)
  console.log(`- 成功率: ${Math.round(successCount / totalCount * 100)}%`)
  
  if (successCount === totalCount) {
    console.log('🎉 所有测试通过！浏览器环境配置获取正常工作')
    console.log('✅ 已成功去除本地 config.json 引用，改为通过 3031 端口获取配置')
  } else {
    console.log('⚠️  部分测试失败，需要进一步检查')
    results.forEach((result, index) => {
      if (!result.success) {
        console.log(`   测试 ${index + 1} 失败: ${result.error}`)
      }
    })
  }
  
  process.exit(successCount === totalCount ? 0 : 1)
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('测试运行失败:', error)
    process.exit(1)
  })
}

module.exports = { 
  testMainProcessConfigEndpoint, 
  testElectronApiConfig, 
  testDeviceInfo, 
  testStoreConfigLoad 
}
