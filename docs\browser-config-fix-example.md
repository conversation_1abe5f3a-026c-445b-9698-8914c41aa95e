# 浏览器环境配置获取修复示例

## 🎯 修复目标

去除浏览器开发模式中对本地 `config.json` 文件的直接引用，改为通过 3030 端口的 HTTP 服务获取配置。

## 🔧 修复前后对比

### 修复前（有问题的代码）

```javascript
// src/renderer/src/store/config/index.js
const loadConfig = () => {
  return new Promise(async (resolve, reject) => {
    try {
      if (window.api && typeof window.api !== 'undefined') {
        // Electron 环境：通过原生 API 获取配置
        const config = await window.api.getConfig()
        // ... 处理配置
      } else {
        // 浏览器环境：直接导入本地文件 ❌ 问题所在
        const config = await import('../../../../../config.json')
        // ... 处理配置
      }
      resolve(true)
    } catch (error) {
      reject(error)
    }
  })
}
```

**问题**:
1. 浏览器环境下直接导入 `../../../../../config.json` 文件
2. 该文件可能不存在或路径错误
3. 破坏了统一 API 的设计理念
4. 无法利用主进程的配置管理功能

### 修复后（正确的代码）

```javascript
// src/renderer/src/store/config/index.js
import electronApi from '@/utils/electronApi'

const loadConfig = () => {
  return new Promise(async (resolve, reject) => {
    try {
      // 统一使用 electronApi 获取配置，自动适配环境 ✅
      const config = await electronApi.config.getConfig()
      
      if (config && config.APP) {
        const appConfig = config.APP
        setConfig(appConfig)
        setName(appConfig.name)
        
        if (appConfig.pwd) {
          setPwd(appConfig.pwd)
        } else {
          // 获取机器码作为默认密码
          const machineInfo = await electronApi.device.getMachineInfo()
          setPwd(machineInfo.machineId)
        }
      } else {
        // 使用默认配置作为回退
        console.warn('未获取到配置，使用默认配置')
        const defaultAppConfig = {
          style: "",
          alwaysOnTop: false,
          fullScreen: false,
          name: "",
          pwd: ""
        }
        setConfig(defaultAppConfig)
        setName(defaultAppConfig.name)
        
        // 获取机器码作为默认密码
        const machineInfo = await electronApi.device.getMachineInfo()
        setPwd(machineInfo.machineId)
      }
      
      resolve(true)
    } catch (error) {
      console.error('读取配置文件失败:', error)
      reject(error)
    }
  })
}
```

**优势**:
1. 统一使用 `electronApi.config.getConfig()` 获取配置
2. 自动适配 Electron 和浏览器环境
3. 浏览器环境下通过 3030 端口的 HTTP 服务获取配置
4. 提供完整的错误处理和默认配置回退机制

## 🚀 工作原理

### Electron 环境
```
Vue Store → electronApi.config.getConfig() → window.api.getConfig() → 主进程配置管理器
```

### 浏览器环境
```
Vue Store → electronApi.config.getConfig() → HTTP GET /api/config/get → 主进程 HTTP 服务 → 主进程配置管理器
```

## 🧪 测试验证

### 1. 运行测试脚本

```bash
# 确保 Electron 应用正在运行
npm run dev

# 在另一个终端运行测试
node scripts/test-browser-config.js
```

### 2. 手动测试

#### 在浏览器中测试
1. 启动 Electron 应用：`npm run dev`
2. 在浏览器中访问：`http://localhost:5173`
3. 打开浏览器开发者工具
4. 在控制台中运行：

```javascript
// 测试配置获取
import('@/utils/electronApi').then(async (module) => {
  const electronApi = module.default
  const config = await electronApi.config.getConfig()
  console.log('配置获取结果:', config)
})
```

#### 在 Electron 中测试
1. 启动应用：`npm run dev`
2. 打开开发者工具：`Ctrl+Shift+I`
3. 在控制台中运行相同的代码

### 3. 验证网络请求

在浏览器环境下，打开开发者工具的 Network 标签页，应该能看到：
- `GET http://localhost:3030/api/config/get` - 获取配置
- `GET http://localhost:3030/api/device/machine-info` - 获取机器信息

## 📊 修复效果

### ✅ 解决的问题

1. **去除本地文件依赖** - 浏览器环境不再依赖本地 `config.json` 文件
2. **统一 API 接口** - 所有环境都使用 `electronApi.config.getConfig()`
3. **完整功能支持** - 浏览器环境支持完整的配置管理功能
4. **错误处理改进** - 提供默认配置回退机制

### 🔧 新增功能

1. **环境自动适配** - 自动检测并选择合适的配置获取方式
2. **默认配置回退** - 当配置获取失败时使用默认配置
3. **完整错误处理** - 详细的错误日志和用户友好的错误提示

## 📚 相关文档

- `docs/electron-api-integration.md` - 统一 API 集成指南
- `docs/electron-api-usage.md` - API 使用说明
- `docs/config-api-fix-summary.md` - 配置 API 修复总结
- `scripts/test-browser-config.js` - 浏览器环境测试脚本

## 🎉 总结

通过这次修复，我们成功实现了：

1. **完全去除了浏览器环境对本地 config.json 文件的依赖**
2. **统一了配置获取接口，所有环境都使用 electronApi**
3. **确保了浏览器开发模式的完整功能支持**
4. **提供了完善的测试工具和文档**

现在，无论是在 Electron 环境还是浏览器环境下，配置获取都通过统一的 API 进行，真正实现了"一套代码，多环境运行"的目标！
