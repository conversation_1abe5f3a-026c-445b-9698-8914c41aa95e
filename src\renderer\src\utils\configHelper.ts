/**
 * 配置助手工具
 * 提供简化的配置访问接口，兼容现有代码
 */

import configManager from './configManager'

/**
 * 获取配置值的简化接口
 * @param keyPath 配置路径，如 'common.background'
 * @param defaultValue 默认值
 * @returns 配置值
 */
export function getConfig(keyPath: string, defaultValue?: any): any {
  return configManager.getConfigValue(keyPath, defaultValue)
}

/**
 * 获取资源URL的简化接口
 * @param keyPath 配置路径，如 'common.background'
 * @returns 资源URL或null
 */
export function getResourceUrl(keyPath: string): string | null {
  return configManager.getResourceUrl(keyPath)
}

/**
 * 获取图片资源URL，如果是本地资源则返回本地HTTP服务URL
 * @param keyPath 配置路径
 * @returns 图片URL
 */
export function getImageUrl(keyPath: string): string {
  const url = configManager.getResourceUrl(keyPath)
  return url || ''
}

/**
 * 获取背景图片URL
 */
export function getBackgroundUrl(): string {
  return getImageUrl('common.background')
}

/**
 * 获取Logo URL
 */
export function getLogoUrl(): string {
  return getImageUrl('common.logo')
}

/**
 * 获取相机按钮组配置
 */
export function getCameraButtonConfig(): Record<string, string> {
  const buttons = getConfig('common.cameraBtnGroup', {})
  const result: Record<string, string> = {}
  
  for (const [key, value] of Object.entries(buttons)) {
    if (typeof value === 'string') {
      const url = configManager.getResourceUrl(`common.cameraBtnGroup.${key}`)
      if (url) {
        result[key] = url
      }
    }
  }
  
  return result
}

/**
 * 获取相机按钮URL
 * @param buttonName 按钮名称，如 'back', 'buy', 'check', 'retake'
 */
export function getCameraButtonUrl(buttonName: string): string {
  return getImageUrl(`common.cameraBtnGroup.${buttonName}`)
}

/**
 * 检查配置是否已加载
 */
export function isConfigLoaded(): boolean {
  return !!configManager.getCurrentConfig()
}

/**
 * 获取当前配置版本
 */
export function getCurrentConfigVersion(): string | null {
  return configManager.getCurrentVersion()
}

/**
 * 刷新配置（重新从API获取并下载资源）
 * @param apiUrl API地址，默认为 '/api/v1.0/device/config'
 */
export async function refreshConfig(apiUrl: string = '/api/v1.0/device/config'): Promise<boolean> {
  try {
    const axios = (await import('axios')).default
    const response = await axios.get(apiUrl)

    if (response.data && response.data.data) {
      const result = await configManager.downloadConfigResources(response.data.data)
      return result.success
    }

    return false
  } catch (error) {
    console.error('刷新配置失败:', error)
    return false
  }
}

/**
 * 检查配置更新
 * @param apiUrl API地址，默认为 '/api/v1.0/device/config'
 */
export async function checkForUpdates(apiUrl: string = '/api/v1.0/device/config') {
  return await configManager.checkForUpdates(apiUrl)
}

/**
 * 手动更新配置
 * @param apiUrl API地址，默认为 '/api/v1.0/device/config'
 */
export async function updateConfig(apiUrl: string = '/api/v1.0/device/config') {
  return await configManager.updateConfig(apiUrl)
}

/**
 * 创建响应式配置获取器（用于Vue组件）
 * @param keyPath 配置路径
 * @param defaultValue 默认值
 * @returns 响应式引用
 */
export function useConfig(keyPath: string, defaultValue?: any) {
  const { ref, computed } = require('vue')
  
  return computed(() => {
    return configManager.getConfigValue(keyPath, defaultValue)
  })
}

/**
 * 创建响应式资源URL获取器（用于Vue组件）
 * @param keyPath 配置路径
 * @returns 响应式引用
 */
export function useResourceUrl(keyPath: string) {
  const { computed } = require('vue')
  
  return computed(() => {
    return configManager.getResourceUrl(keyPath) || ''
  })
}

/**
 * 批量获取配置值
 * @param keyPaths 配置路径数组
 * @returns 配置值对象
 */
export function getBatchConfig(keyPaths: string[]): Record<string, any> {
  const result: Record<string, any> = {}
  
  for (const keyPath of keyPaths) {
    result[keyPath] = configManager.getConfigValue(keyPath)
  }
  
  return result
}

/**
 * 批量获取资源URL
 * @param keyPaths 配置路径数组
 * @returns 资源URL对象
 */
export function getBatchResourceUrls(keyPaths: string[]): Record<string, string> {
  const result: Record<string, string> = {}
  
  for (const keyPath of keyPaths) {
    const url = configManager.getResourceUrl(keyPath)
    if (url) {
      result[keyPath] = url
    }
  }
  
  return result
}

/**
 * 监听配置变化（当配置更新时触发回调）
 * @param callback 回调函数
 * @returns 取消监听的函数
 */
export function watchConfig(callback: (config: any) => void): () => void {
  let currentVersion = configManager.getCurrentVersion()
  
  const checkInterval = setInterval(() => {
    const newVersion = configManager.getCurrentVersion()
    if (newVersion !== currentVersion) {
      currentVersion = newVersion
      callback(configManager.getCurrentConfig())
    }
  }, 1000)
  
  return () => {
    clearInterval(checkInterval)
  }
}

// 导出配置管理器实例，供高级用法使用
export { configManager }

// 默认导出常用函数
export default {
  getConfig,
  getResourceUrl,
  getImageUrl,
  getBackgroundUrl,
  getLogoUrl,
  getCameraButtonConfig,
  getCameraButtonUrl,
  isConfigLoaded,
  getCurrentConfigVersion,
  refreshConfig,
  checkForUpdates,
  updateConfig,
  useConfig,
  useResourceUrl,
  getBatchConfig,
  getBatchResourceUrls,
  watchConfig,
  configManager
}
