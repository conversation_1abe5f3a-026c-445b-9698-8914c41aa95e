import express from 'express'
import cors from 'cors'
import { Server } from 'http'
import bodyParser from 'body-parser'
import { BrowserWindow, app, ipcMain } from 'electron'
import { incrementAndUpdateVersion } from '../../../scripts/increment-version'
import configResourceManager from './configResourceManager'
import path from 'path'
import fs from 'fs'
import getConfig, { setLocalConfig } from '../readConfig'
import { getVersions, getCurrentVersion } from '../getVersion'
import machineId from 'node-machine-id'
import { keyboard, Key } from '@nut-tree-fork/nut-js'

// 存储最新的图像数据
let latestImageData = {
//   time: '',
  img: '',
  timestamp: Date.now()
}

// 创建 Express 应用
const app = express()
let server: Server | null = null

// 配置中间件
app.use(cors())
app.use(bodyParser.json({ limit: '50mb' }))
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }))

// 初始化 HTTP 服务
function initHttpService(): void {
  // 获取最新图像数据的接口
  app.get('/getVersion', (req, res) => {
    res.json(incrementAndUpdateVersion())
  })
  app.get('/getImg', (req, res) => {
    res.json(latestImageData)
  })

  // 直接返回图片数据的测试接口
  app.get('/getImgTest', (req, res) => {
    if (!latestImageData.img) {
      return res.status(404).json({
        success: false,
        message: '没有可用的图片数据'
      })
    }
    
    // 从 Base64 字符串中提取实际的图片数据
    const base64Data = latestImageData.img.replace(/^data:image\/\w+;base64,/, '')
    const imageBuffer = Buffer.from(base64Data, 'base64')
    
    // 设置响应头
    res.set('Content-Type', 'image/jpeg') // 根据实际图片类型调整
    res.set('Content-Length', imageBuffer.length.toString())
    
    // 发送图片数据
    res.send(imageBuffer)
  })

  // 添加设置状态的接口
  app.post('/setStatus', (req, res) => {
    const { status } = req.body
    
    if (!status) {
      return res.status(400).json({ 
        success: false, 
        message: '缺少状态参数' 
      })
    }
    
    // 向渲染进程发送状态更新事件
    const mainWindow = BrowserWindow.getAllWindows()[0]
    if (mainWindow) {
      mainWindow.webContents.send('status-update', status)
      console.log(`状态已更新为: ${status}`)
      
      return res.json({ 
        success: true, 
        message: `状态已更新为: ${status}`,
        timestamp: new Date().toISOString()
      })
    } else {
      return res.status(500).json({ 
        success: false, 
        message: '主窗口未找到' 
      })
    }
  })

  // 健康检查接口
  app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() })
  })

  // ============ Electron API 中继路由 ============

  // 应用控制相关 API
  app.post('/api/app/close', (req, res) => {
    try {
      const mainWindow = BrowserWindow.getAllWindows()[0]
      if (mainWindow) {
        mainWindow.close()
      }
      res.json({ success: true, message: '应用关闭请求已处理' })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  app.post('/api/app/restart', (req, res) => {
    try {
      app.relaunch()
      app.exit()
      res.json({ success: true, message: '应用重启请求已处理' })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  app.get('/api/app/path', (req, res) => {
    try {
      const appPath = path.dirname(app.getPath('exe'))
      res.json({ path: appPath })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  app.get('/api/app/dev', (req, res) => {
    try {
      const env = process.env.NODE_ENV || 'production'
      res.json({ env })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  // 配置相关 API
  app.get('/api/config/get', (req, res) => {
    try {
      const config = getConfig()
      res.json(config)
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  app.post('/api/config/set', (req, res) => {
    try {
      const { params } = req.body
      setLocalConfig(...params)
      res.json({ success: true, message: '配置设置成功' })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  // 获取本地配置 - 支持 GET 请求
  app.get('/api/config/local', (req, res) => {
    try {
      const version = req.query.version as string
      const config = configResourceManager.getLocalConfig(version)
      if (config) {
        res.json({
          success: true,
          config,
          version: version || configResourceManager.getCurrentVersion()
        })
      } else {
        res.status(404).json({
          success: false,
          error: version ? `Config version ${version} not found` : 'No config available'
        })
      }
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  // 获取本地配置 - 兼容 POST 请求
  app.post('/api/config/local', (req, res) => {
    try {
      const { version } = req.body
      const config = configResourceManager.getLocalConfig(version)
      if (config) {
        res.json({
          success: true,
          config,
          version: version || configResourceManager.getCurrentVersion()
        })
      } else {
        res.status(404).json({
          success: false,
          error: version ? `Config version ${version} not found` : 'No config available'
        })
      }
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  app.post('/api/config/download', async (req, res) => {
    try {
      const { configData } = req.body
      const result = await configResourceManager.downloadConfigResources(configData)
      res.json(result)
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  // 获取配置下载进度
  app.get('/api/config/progress', (req, res) => {
    try {
      const progress = configResourceManager.getDownloadProgress()
      res.json({
        success: true,
        progress
      })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  app.get('/api/config/versions', (req, res) => {
    try {
      const versions = configResourceManager.getAvailableVersions()
      res.json({
        success: true,
        versions,
        currentVersion: configResourceManager.getCurrentVersion()
      })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  app.post('/api/config/cleanup', (req, res) => {
    try {
      const { keepCount } = req.body
      // 这里应该调用实际的清理方法，暂时返回模拟结果
      res.json({
        success: true,
        message: `已清理旧版本，保留最新 ${keepCount || 3} 个版本`,
        cleanedVersions: []
      })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  app.post('/api/config/version/set', (req, res) => {
    try {
      const { version } = req.body
      configResourceManager.setCurrentVersion(version)
      res.json({ success: true, message: `版本设置为 ${version}` })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  // 设备信息相关 API
  app.get('/api/device/machine-info', (req, res) => {
    try {
      const machineID = machineId.machineIdSync(true)
      res.json({ machineId: machineID })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  // 版本相关 API
  app.get('/api/version/current', (req, res) => {
    try {
      const version = getCurrentVersion()
      res.json({ version })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  app.get('/api/version/info', (req, res) => {
    try {
      const versionInfo = getVersions()
      res.json({ versionInfo })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  // 更新相关 API
  app.get('/api/update/check', (req, res) => {
    try {
      // 这里应该调用实际的更新检查逻辑
      res.json({
        hasUpdate: false,
        currentVersion: getCurrentVersion(),
        latestVersion: getCurrentVersion(),
        message: '当前已是最新版本'
      })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  app.post('/api/update/download', (req, res) => {
    try {
      // 这里应该调用实际的更新下载逻辑
      res.json({
        success: true,
        message: '更新下载完成',
        downloadPath: '/mock/update/path'
      })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  app.post('/api/update/install', (req, res) => {
    try {
      // 这里应该调用实际的更新安装逻辑
      res.json({
        success: true,
        message: '更新安装完成'
      })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  app.get('/api/update/progress', (req, res) => {
    try {
      const progress = configResourceManager.getDownloadProgress()
      res.json({ progress })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  // 开发者工具相关 API
  app.post('/api/dev/toggle-devtools', (req, res) => {
    try {
      const mainWindow = BrowserWindow.getAllWindows()[0]
      if (mainWindow) {
        if (mainWindow.webContents.isDevToolsOpened()) {
          mainWindow.webContents.closeDevTools()
        } else {
          mainWindow.webContents.openDevTools({ mode: 'undocked' })
        }
        res.json({ success: true, message: '开发者工具切换完成' })
      } else {
        res.status(404).json({ success: false, error: '主窗口未找到' })
      }
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  // 快捷键相关 API
  app.post('/api/shortcut/execute', async (req, res) => {
    try {
      const { params } = req.body
      if (params?.name === 'huanlianopen' || params?.name === 'huanlianclose') {
        await keyboard.type(Key.LeftControl, Key.Z)
      }
      res.json({
        success: true,
        message: `快捷键操作 ${params?.name} 执行完成`
      })
    } catch (error) {
      res.status(500).json({ success: false, error: error.message })
    }
  })

  // 配置资源相关接口

  // 下载并缓存配置资源
  app.post('/download-config-resources', async (req, res) => {
    try {
      const configData = req.body

      if (!configData || !configData.config_version) {
        return res.status(400).json({
          success: false,
          error: 'Invalid config data: missing config_version'
        })
      }

      console.log(`Starting download for config version: ${configData.config_version}`)
      const result = await configResourceManager.downloadConfigResources(configData)

      if (result.success) {
        res.json({
          success: true,
          version: configData.config_version,
          message: 'All resources downloaded successfully'
        })
      } else {
        res.status(500).json({
          success: false,
          version: configData.config_version,
          errors: result.errors,
          message: 'Some resources failed to download'
        })
      }
    } catch (error) {
      console.error('Error downloading config resources:', error)
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  })

  // 获取本地配置
  app.get('/local-config/:version?', (req, res) => {
    try {
      const version = req.params.version
      const config = configResourceManager.getLocalConfig(version)

      if (!config) {
        return res.status(404).json({
          success: false,
          error: version ? `Config version ${version} not found` : 'No config available'
        })
      }

      res.json({
        success: true,
        config,
        version: version || configResourceManager.getCurrentVersion()
      })
    } catch (error) {
      console.error('Error getting local config:', error)
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  })

  // 获取下载进度
  app.get('/download-progress', (req, res) => {
    try {
      const progress = configResourceManager.getDownloadProgress()
      res.json({
        success: true,
        progress
      })
    } catch (error) {
      console.error('Error getting download progress:', error)
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  })

  // 获取可用版本列表
  app.get('/available-versions', (req, res) => {
    try {
      const versions = configResourceManager.getAvailableVersions()
      res.json({
        success: true,
        versions,
        currentVersion: configResourceManager.getCurrentVersion()
      })
    } catch (error) {
      console.error('Error getting available versions:', error)
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  })

  // 静态资源服务 - 服务配置资源文件
  app.use('/config-resources', (req, res, next) => {
    // 获取请求的文件路径
    const requestPath = req.path
    const segments = requestPath.split('/').filter(Boolean)

    if (segments.length < 2) {
      return res.status(404).json({ error: 'Invalid resource path' })
    }

    const version = segments[0]
    const filename = segments.slice(1).join('/')

    const resourcePath = configResourceManager.getResourcePath(version, filename)

    if (!resourcePath || !fs.existsSync(resourcePath)) {
      return res.status(404).json({ error: 'Resource not found' })
    }

    // 根据文件扩展名设置正确的 Content-Type
    const ext = path.extname(resourcePath).toLowerCase()
    switch (ext) {
      case '.png':
        res.setHeader('Content-Type', 'image/png')
        break
      case '.jpg':
      case '.jpeg':
        res.setHeader('Content-Type', 'image/jpeg')
        break
      case '.gif':
        res.setHeader('Content-Type', 'image/gif')
        break
      case '.svg':
        res.setHeader('Content-Type', 'image/svg+xml')
        break
      case '.webp':
        res.setHeader('Content-Type', 'image/webp')
        break
      case '.mp4':
        res.setHeader('Content-Type', 'video/mp4')
        break
      case '.mp3':
        res.setHeader('Content-Type', 'audio/mpeg')
        break
      case '.json':
        res.setHeader('Content-Type', 'application/json')
        break
      default:
        res.setHeader('Content-Type', 'application/octet-stream')
    }

    // 设置缓存头
    res.setHeader('Cache-Control', 'public, max-age=31536000') // 1年缓存

    // 发送文件
    res.sendFile(resourcePath)
  })


  // 启动服务器
  const PORT = 3031
  server = app.listen(PORT, () => {
    console.log(`HTTP server start: Listen ${PORT}`)
  })

  // 错误处理
  server.on('error', (err) => {
    console.error(`HTTP server error: ${err.message}`)
  })
}

// 更新图像数据
function updateImageData(time: string, img: string): void {
  latestImageData = {
    // time,
    img,
    timestamp: Date.now()
  }
  console.log(`图像数据已更新，时间戳: ${time}`)
}

// 关闭 HTTP 服务
function closeHttpService(): void {
  if (server) {
    server.close(() => {
      console.log('HTTP 服务器已关闭')
    })
    server = null
  }
}

export default {
  initHttpService,
  updateImageData,
  closeHttpService
}