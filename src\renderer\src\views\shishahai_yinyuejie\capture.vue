<template>
  <div class="capture">
    <img class="titlebk" src="@/assets/images/popofifititle.png">
    <!-- 背景图 -->
    <!-- <img class="bg-image" src="@/assets/camera/bg.png" /> -->

    <!-- 半透明遮罩 -->
    <!-- <img class="mask-image" src="@/assets/camera/mask.png" /> -->

    <!-- 外圈图包裹 faceCapture -->
    <div class="circle-outer">

      <!-- faceCapture + 摄像头画面 + 内圈 -->
      <div class="faceCapture">
        <video
          autoplay
          playsinline
          ref="videoDom"
          class="videoDom"
          v-show="!uploadParams.image"
          :width="videoDomWidth"
          :height="videoDomHeight"
        ></video>

        <canvas
          ref="canvasDom"
          v-show="uploadParams.image"
          class="canvasDom"
          :width="videoDomWidth"
          :height="videoDomHeight"
        ></canvas>
        <canvas
          ref="canvasDom2"
          v-show="false"
          class="canvasDom canvasDom2"
          :width="videoDomHeight"
          :height="videoDomWidth"
        ></canvas>
<!--        <span class="number">{{ number || '' }}</span>-->
      </div>
    </div>
    <div v-if="!uploadParams.image" class="btnGroup">
      <template v-if="!cameraReady">
        <div class="loading-text">
          正在打开摄像头<span class="dots">...</span>
        </div>
        <label class="btnUploadInput">
          上传本地图片
          <input type="file" accept="image/*" @change="handleFileUpload" hidden />
        </label>
      </template>
      <template v-else>
        <div v-if="!showButtons">
          <div class="btnCapture" @click="handleCapture">拍照</div>
          <label class="btnUploadInput">
            上传本地图片
            <input type="file" accept="image/*" @change="handleFileUpload" hidden />
          </label>
        </div>
      </template>
    </div>

    <!-- 提示和按钮 -->
    <div class="title" v-if="showTitle">请保持脸部在拍照框内</div>
    <div class="btnWarp" v-if="showButtons">
      <div class="btnConfirm" @click="upload()">确认继续</div>
      <div class="btnRetake" @click="reCapture()">重拍一张</div>
    </div>
    <!-- <div class="btn" @click="back()">返回</div> -->
    <!-- <div class="btn2" @click="toKatong()">卡通</div> -->
    <div v-if="showError" class="error-toast">
      {{ errorMessage }}
    </div>
<div class="user-ag-checkbox" v-if="!showUserAg">
      <input type="checkbox" class="checkbox" v-model="checked" />
      <span  @click="showUserAg = true">用户服务和隐私授权协议</span>
    </div>
    <div class="user-agreement" v-if="showUserAg">
      <div class="content">
      <div class="head">POPOFIFI用户协议</div>

      <div>欢迎您使用POPOFIFI专属卡通定制服务！</div>
      <div>在您使用本服务前，POPOFIFI 开发者北京天翔睿翼科技有限公司向您郑重做如下告知：</div>
      <div class="label">基础提示</div>
      <div>在使用POPOFIFI为您提供的专属卡通定制服务之前，请您务必审慎阅读、充分理解本告知(如果您未满16周岁，或已满16周岁未满18周岁且不能以自己的劳动收入作为主要收入来源的，请在法定监护人的陪同下阅读本告知）各项条款，特别是限制或免除责任条款、隐私保护条款等，以加粗加黑和/或加下划线等显示形式提示您注意的重要条款，请务必重点查阅。

       </div>

      <div>
      若您不同意本告知，则您有充分且完全的权利退出POPOFIFI专属卡通定制服务，您实际使用POPOFIFI专属卡通定制服务的行为即视为您已阅读、理解并同意接受本告知。如果您对本告知有任何的疑问、投诉、意见和建议，欢迎您通过本告知所附联系方式与我们沟通反馈。
      </div>
      <div class="label">服务内容</div>

      POPOFIFI为您提供丰富的服务，用户只需简单操作，即可快速捕捉面部图像，依托前沿AI智能生成技术，瞬间生成特色主题卡通照片并进一步定制成别具一格的潮流玩具、小挂件等。
      您同意并知悉，POPOFIFI的具体服务内容、功能和形式依据实际情况的不同按实际可见的状态提供，我们有权自行确定POPOFIFI服务的具体内容、功能和形式，有权自行决定增加、变更、中断和停止POPOFIFI具体的内容、功能和形式。具体以旅拍机实时呈现的服务内容、功能和形式为准。

      <div class="label">信息适用与存储豁免</div>
      当您使用POPOFIFI拍照或上传照片时，我们会收集您拍摄的照片信息。这些信息会通过AI智能生成技术，用于生成特色的卡通形象。同时为您提供更完美的服务体验，该信息也将会纳入我们的数据库，我们会进行科学管理。
      <div class="bold">对于收集到的照片信息，我们会先进行匿名化处理，将其转化为无法识别您个人身份的形式之后再以数据的形式加以存储，存储过程中我们始终严格遵循隐私保护原则，不遗余力地保障您的信息安全。
      </div>
      <div class="bold">对于生成的卡通形象，除另行约定之外，我们有权在保护您的身份与隐私的前提下，用于线上宣传与展示：抖音/小红书等平台推广；线下宣传与展示：门店陈列、展会灯箱；衍生创作：允许AI生成二次设计稿、视频、2D和3D数字人等。
      </div>
      <div>当您使用POPOFIFI进行拍照操作时，即代表您认可并同意我们有权依据此信息保护规则，对与您的照片等信息进行使用与存储。考虑到数据库优化的实际需求，就信息数据的存储事项，您同意豁免我们另行单独征得您的授权。
      </div>
      <div>如何联系我们</div>
      <div>如果您有任何的疑问、投诉、意见和建议，欢迎您与我们沟通反馈。我们的联系方式见下：</div>

        <div class="foot">客服热线： 010-88738088 </div>
        <div class="foot">客服邮箱： <EMAIL> </div>
        <div class="foot">日期： 2025 年 05 月 01 日</div>
      </div>
      <div class="footer">
        <button @click="showUserAg = false">我以知悉</button>
      </div>


    </div>
  </div>
</template>

<script>
import  './uploadImg.js';
import { generateUUID } from "@/utils/uuid";
import electronApi from '@/utils/electronApi';
export default {
  name: "capture",
  data() {
    return {
      context: null,
      timeCapture: null,
      // number: 3,
      uploadParams: {
        option: "002",
        image: "",
        userId: "",
      },
      isPrevent: false,
      qqq: false,
      rotateVideo: false,
      ratio: 1,
      stream: null,
      showButtons: false,
      showTitle: true,
      showError:false,
      errorMessage: "",
      cameraReady: false,
      showUserAg: false, // 是否显示用户协议
      checked: true,
      showErrorTimeout: null
    };
  },
  computed: {
    videoDomWidth () {
      return 1920 * window.draftScale
    },
    videoDomHeight () {
      return 1080 * window.draftScale
    }
  },
  unmounted() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    if (this.showErrorTimeout) {
      clearTimeout(this.showErrorTimeout);
      this.showErrorTimeout = null;
    }
  },
  watch: {
    showError(newVal) {
      if (newVal) {
        if (this.showErrorTimeout)return
        this.showErrorTimeout = setTimeout(() => {
          this.showError = false;
          this.showErrorTimeout = null
        }, 5000); // 8 秒后自动隐藏
      }
    }
  },
  created() {
    // if (this.$store.state.deviceCode) {
    //   this.rotateVideo = false;
    // } else {
    //   this.rotateVideo = false;
    // }
    window.draftScale = 1;
  },
  async mounted() {
  if (window.piniaAudio) {
    window.piniaAudio.play();
  }

  this.uploadParams.option = this.$route.query.code;
  this.uploadParams.userId = generateUUID();
;

  // 确保 canvas 初始化
  this.$nextTick(() => {
    if (this.$refs.canvasDom) {
      this.context = this.$refs.canvasDom.getContext("2d", {
        willReadFrequently: true,
      });
    } else {
      console.error("Canvas 未找到，context 无法初始化");
    }
  });

  try {
    await this.getUserMedia();
    this.ratio = 4 / 3;
    console.warn("摄像头开启成功，ratio:", this.ratio);
    // this.timeStep();
  } catch (error) {
    this.showErrorToast("摄像头访问异常，请重试或检查权限");
    console.warn("摄像头打开失败", error);

  }
},
  methods: {

    showErrorToast(message) {
      this.errorMessage = message;
      this.showError = true;
      // setTimeout(() => {
      //   this.showError = false;
      // }, 8000); // 8 秒后自动隐藏
    },


    handleCapture() {
      if (!this.checked){
        return this.showErrorToast("请同意用户服务和隐私授权协议");
      }
      this.capture();
      try{
        const electronApi = await import('@/utils/electronApi')
        electronApi.default.shortcut.kuaijiejian({name: 'huanlianclose'})
      }catch (e) {
        console.warn(e)
      }
      setTimeout(async () => {
        this.capture2();
        try{
          const electronApi = await import('@/utils/electronApi')
          electronApi.default.shortcut.kuaijiejian({name: 'huanlianclose'})
        }catch (e) {
          console.error(e)
        }
      }, 300);
      this.showButtons = true;
      this.showTitle = false;
    },

    handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          this.drawUploadedImageToCanvas(img);
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    },


    drawUploadedImageToCanvas(img) {
      const canvas = this.$refs.canvasDom;
      const ctx = this.context;
      const width = this.videoDomWidth;
      const height = this.videoDomHeight;

      // 设置 canvas 宽高对调（因为我们要顺时针旋转90°）
      canvas.width = height;
      canvas.height = width;

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      ctx.save();
      ctx.translate(canvas.width / 2, canvas.height / 2);
      ctx.rotate(-90 * Math.PI / 180); // 顺时针90度
      ctx.translate(-width / 2, -height / 2);

      // 计算绘图区域，等比缩放并居中
      const imgAspect = img.width / img.height;
      const canvasAspect = width / height;

      let drawWidth, drawHeight, offsetX, offsetY;
      if (imgAspect > canvasAspect) {
        drawWidth = width;
        drawHeight = width / imgAspect;
        offsetX = 0;
        offsetY = (height - drawHeight) / 2;
      } else {
        drawHeight = height;
        drawWidth = height * imgAspect;
        offsetX = (width - drawWidth) / 2;
        offsetY = 0;
      }

      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
      ctx.restore();


      // 原图尺寸
      const rawWidth = img.width;
      const rawHeight = img.height;

      // 创建旋转后画布（宽高对调）
      const rotatedCanvas = document.createElement("canvas");
      rotatedCanvas.width = rawWidth;
      rotatedCanvas.height = rawHeight;

      const rotatedCtx = rotatedCanvas.getContext("2d");

      // 平移到中心并旋转
      rotatedCtx.translate(rotatedCanvas.width / 2, rotatedCanvas.height / 2);
      // rotatedCtx.rotate(-90 * Math.PI / 180);

      // 将图像画到旋转画布中心
      rotatedCtx.drawImage(
        img,
        -rawWidth / 2,
        -rawHeight / 2,
        rawWidth,
        rawHeight
      );

      // 输出 Base64
      this.uploadParams.image = rotatedCanvas.toDataURL("image/jpeg");

      this.showButtons = true;
      this.showTitle = false;

    },



    async getUserMedia() {
      return new Promise(async (resolve, reject) => {
        try {
          // 🛠 1️⃣ 先请求摄像头权限
          // let tempStream = await navigator.mediaDevices.getUserMedia({ video: true });
          // tempStream.getTracks().forEach(track => track.stop()); // 关闭临时流

          // 🛠 2️⃣ 重新获取设备列表，确保 `deviceId` 可用
          let devices = await navigator.mediaDevices.enumerateDevices();
          console.warn("所有设备:", devices);
          
          let videoDevices = devices.filter((device) => device.kind === "videoinput" && device.label.indexOf('Snap')!= -1);
          if (videoDevices.length === 0) {
            this.showErrorToast("未找到摄像头设备");
            return reject(new Error("没有找到可用的摄像头"));
          }

          // 🛠 3️⃣ 过滤指定的摄像头设备
          let middleDevice = videoDevices.filter((device) => {
            return (
              device.label.includes("LRCP  500W 02") ||
              device.label.includes("c922 Pro Stream Webcam") ||
              device.label.includes("OBSBOT Meet StreamCamera") ||
              device.label.includes("OBSBOT Virtual Camera")
            );
          });

          // 🛠 4️⃣ 如果找不到特定摄像头，就使用第一个可用的摄像头
          let selectedDevice = middleDevice.length > 0 ? middleDevice[0] : videoDevices[0];

          let constraints = {
            video: {
              deviceId: selectedDevice.deviceId ? { exact: selectedDevice.deviceId } : undefined,
              width: { ideal: 1920 },
              height: { ideal: 1080 },
              frameRate: { ideal: 30 } // 可选：提升帧率
            },
            audio: false
          };

          // 🛠 5️⃣ 再次获取摄像头视频流
          let stream = await navigator.mediaDevices.getUserMedia(constraints);
          console.log("摄像头成功打开", stream);
          this.$refs.videoDom.srcObject = stream;
          this.stream = stream;
          this.$refs.videoDom.onloadedmetadata = () => {
            this.cameraReady = true;
            resolve();
          };
        } catch (error) {
          this.showErrorToast("摄像头访问异常，请重试或检查权限");
          reject(error);
        }
      });
    },


    draw() {
      if (!this.$refs.videoDom) {
        return;
      }
    },

    capture() {
      this.uploadParams.userId = generateUUID(); // 生成新的 UUID
      console.log("新的 userId:", this.uploadParams.userId);
      // 确保 canvas 组件存在
      if (!this.$refs.canvasDom) {
        console.error("Canvas 组件未找到");
        return;
      }
      if (!this.context) {
        console.error("Canvas context 为空，无法进行捕捉");
        return;
      }
      cancelAnimationFrame(this.timeCapture);

      // 确保 video 组件存在
      if (!this.$refs.videoDom) {
        console.error("Video 组件未找到");
        return;
      }

      // 获取 video 真实尺寸（摄像头原始分辨率）
      let video = this.$refs.videoDom;
      let videoWidth = video.videoWidth;
      let videoHeight = video.videoHeight;

      if (videoWidth === 0 || videoHeight === 0) {
        console.error("视频流未加载，无法捕捉");
        return;
      }

      console.warn("视频原始宽高:", videoWidth, videoHeight);

      // 获取 canvas 的显示尺寸
      let canvasWidth = this.videoDomWidth;
      let canvasHeight = this.videoDomHeight;

      // 计算最佳绘制尺寸，保持原始比例
      let videoAspectRatio = videoWidth / videoHeight;
      let canvasAspectRatio = canvasWidth / canvasHeight;

      let drawWidth, drawHeight, offsetX, offsetY;

      if (videoAspectRatio > canvasAspectRatio) {
        // 视频更宽，以高度为基准适配
        drawHeight = canvasHeight;
        drawWidth = videoWidth * (canvasHeight / videoHeight);
        offsetX = (canvasWidth - drawWidth) / 2;
        offsetY = 0;
      } else {
        // 视频更窄，以宽度为基准适配
        drawWidth = canvasWidth;
        drawHeight = videoHeight * (canvasWidth / videoWidth);
        offsetX = 0;
        offsetY = (canvasHeight - drawHeight) / 2;
      }

      console.warn("绘制区域: ", drawWidth, drawHeight, offsetX, offsetY);

      this.context.clearRect(0, 0, canvasWidth, canvasHeight);

      this.context.save();

      this.context.drawImage(
        video,
        0, 0, videoWidth, videoHeight,
        offsetX, offsetY, drawWidth, drawHeight
      );

      this.context.restore();


      console.warn("捕捉到的旋转图像数据：", this.uploadParams.image);
    },
    
    capture2() {
      // 确保 canvas2 组件存在
      if (!this.$refs.canvasDom2) {
        console.error("Canvas2 组件未找到");
        return;
      }
      
      // 获取 canvas2 的上下文
      const context2 = this.$refs.canvasDom2.getContext("2d", {
        willReadFrequently: true,
      });
      
      if (!context2) {
        console.error("Canvas2 context 为空，无法进行捕捉");
        return;
      }
      
      // 确保 video 组件存在
      if (!this.$refs.videoDom) {
        console.error("Video 组件未找到");
        return;
      }

      // 获取 video 真实尺寸（摄像头原始分辨率）
      let video = this.$refs.videoDom;
      let videoWidth = video.videoWidth;
      let videoHeight = video.videoHeight;

      if (videoWidth === 0 || videoHeight === 0) {
        console.error("视频流未加载，无法捕捉");
        return;
      }

      // 获取 canvas 的显示尺寸
      let canvasWidth = this.videoDomWidth;
      let canvasHeight = this.videoDomHeight;
      
      // 设置 canvas2 的尺寸
      this.$refs.canvasDom2.width = canvasWidth;
      this.$refs.canvasDom2.height = canvasHeight;

      // 计算最佳绘制尺寸，保持原始比例
      let videoAspectRatio = videoWidth / videoHeight;
      let canvasAspectRatio = canvasWidth / canvasHeight;

      let drawWidth, drawHeight, offsetX, offsetY;

      if (videoAspectRatio > canvasAspectRatio) {
        // 视频更宽，以高度为基准适配
        drawHeight = canvasHeight;
        drawWidth = videoWidth * (canvasHeight / videoHeight);
        offsetX = (canvasWidth - drawWidth) / 2;
        offsetY = 0;
      } else {
        // 视频更窄，以宽度为基准适配
        drawWidth = canvasWidth;
        drawHeight = videoHeight * (canvasWidth / videoWidth);
        offsetX = 0;
        offsetY = (canvasHeight - drawHeight) / 2;
      }

      context2.clearRect(0, 0, canvasWidth, canvasHeight);
      context2.save();
      context2.drawImage(
        video,
        0, 0, videoWidth, videoHeight,
        offsetX, offsetY, drawWidth, drawHeight
      );
      context2.restore();
      // 🎯 离屏 canvas：旋转导出原图（顺时针90°）
      const rawVideo = this.$refs.videoDom;
      const rawWidth = rawVideo.videoWidth;
      const rawHeight = rawVideo.videoHeight;

      const rotatedCanvas = document.createElement("canvas");
      // 注意旋转后宽高对调
      rotatedCanvas.width = rawHeight;
      rotatedCanvas.height = rawWidth;

      const rotatedCtx = rotatedCanvas.getContext("2d");

      // 平移坐标中心，旋转画布
      rotatedCtx.translate(rotatedCanvas.width / 2, rotatedCanvas.height / 2);
      rotatedCtx.rotate(90 * Math.PI / 180);

      // 把原始 video 帧绘制进旋转画布中（无缩放）
      rotatedCtx.drawImage(
        rawVideo,
        -rawWidth / 2,
        -rawHeight / 2,
        rawWidth,
        rawHeight
      );
      // 转为 base64 PNG 作为上传图
      this.uploadParams.image = rotatedCanvas.toDataURL("image/jpeg");
      console.warn("canvasDom2 捕捉完成");
    },
    
    reCapture() {
      try {
        window.api.kuaijiejian({name: 'huanlianopen'})
      }catch (e) {
        console.error(e)
      }
      if (this.isPrevent) {
        return;
      }
      this.uploadParams.image = null;
      this.showButtons = false;
      this.showTitle = true;

      const canvas = this.$refs.canvasDom;
      if (canvas && this.context) {
        this.context.clearRect(0, 0, canvas.width, canvas.height);
      }
      
      const canvas2 = this.$refs.canvasDom2;
      if (canvas2) {
        const context2 = canvas2.getContext("2d");
        if (context2) {
          context2.clearRect(0, 0, canvas2.width, canvas2.height);
        }
      }

      // 重置状态即可，无需重新倒计时
    },


    upload() {
      if (!this.checked){
        return this.showErrorToast("请同意用户服务和隐私授权协议");
      }
      if (this.isPrevent) {
        return;
      }
      

      // ✅ 将 base64 转为 Blob
      const base64Data = this.uploadParams.image;
      const byteString = atob(base64Data.split(',')[1]);
      const mimeString = base64Data.split(',')[0].split(':')[1].split(';')[0];
      const ab = new ArrayBuffer(byteString.length);
      const ia = new Uint8Array(ab);
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }
      const blob = new Blob([ab], { type: mimeString });

      // ✅ 用 Blob 构造一个 File（以便后续上传）
      const subjectFile = new File([blob], "subject.jpg", { type: mimeString });

      // ✅ 使用路由跳转传递文件（推荐方式）
      this.$router.push({
        path: "/rebuild2-themeSelect",
        query: {
          userId: this.uploadParams.userId,
        },
        state: {
          subjectFile
        }
      });
    },

    back() {
      if (this.timer) {
        clearInterval(this.timer);
      }

      this.$router.push({
        path: "/home_capture",
      });
    },
    toKatong() {
      // window.api.kuaijiejian({name: 'huanlianopen'})
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.$router.push({
        path: "/katong3",
      });
    },
  },
};
</script>
<style lang="less" scoped>
.user-ag-checkbox{
  position: fixed;
  bottom: 40rem;
  font-size: 40rem;
  right: 40rem;
  color: #fff;
  z-index: 9999;
}
.checkbox{
  height: 40rem;
  width: 40rem;
}
.user-agreement{
  position: fixed;
  bottom: 100rem;
  left: 100rem;
  border-radius: 30rem;
  width: calc(100vw - 200rem);
  height: 80vh;
  background: #ffffff;
  z-index: 9999;
  padding: 50rem;
  box-sizing: border-box;
  color: #000;
  font-size: 40rem;
  text-align: left;
  
  display:flex;
  flex-direction: column;
  .footer{
    flex: 1;
    button{
      width: 100%;
      height: 80rem;
      line-height: 80rem;
      text-align: center;
      background-color: #287dff;
      border: none;
      font-size: 40rem;
      color: #ffffff;
      font-weight: bold;
      border-radius: 10rem;
      margin:50rem 0 0 0;
      
    }
  }
  .content{
    overflow: scroll;
  }
  .head{
    font-size: 80rem;
    font-weight: bold;
    margin-bottom: 60rem;
  }
  .label{
    font-weight: bold;
    font-size: 50rem;
    margin-top: 30rem;
    margin-bottom: 30rem;
  }
  .bold{
    font-weight: bold;
  }
  .foot{
    text-align: right;
  }
}
.titlebk{
  // height: 200rem
  width: 90vw;
  position:absolute;
  z-index: 555;
  margin-top: 100rem;
  left: 5vw;
}
* {
  margin: 0px;
  padding: 0px;
}

@border: none;

.capture {
  position: relative;
  width: 100vw;
  height: 100vh;
  aspect-ratio: 1440 / 2560;
  background-color: #faf3ec;
  overflow: hidden;

  .bg-image {
    position: fixed;
    top: 0; left: 0;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    z-index: 0;
  }

  .mask-image {
    position: fixed;
    top: 0; left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
    opacity: 1.0;
    pointer-events: none;
  }

  .btnGroup {
    position: absolute;
    top: 82%;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 55rem;
    z-index: 10;

    .btnCapture {
      width: 520rem;
      height: 120rem;
      line-height: 120rem;
      text-align: center;
      background: rgba(255, 255, 255, 0.9);
      font-size: 48rem;
      font-weight: bold;
      color: #000000;
      border-radius: 999rem;
      font-family: "思源黑体";
      margin-bottom: 55rem;
    }

    .btnUploadInput {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 520rem;
      height: 120rem;
      background: transparent;
      border: 4rem solid rgba(255, 255, 255, 0.5);
      font-size: 48rem;
      color: #ffffff;
      font-family: "思源黑体";
      font-weight: bold;
      border-radius: 999rem;
      box-sizing: border-box;
      cursor: pointer;
    }

    .loading-text {
      font-size: 48rem;
      font-weight: bold;
      color: #ffffff;
      font-family: "思源黑体";
      text-align: center;
    }

    .dots::after {
      content: '';
      animation: blinkDots 1s steps(3, end) infinite;
    }

    @keyframes blinkDots {
      0%   { content: ''; }
      33%  { content: '.'; }
      66%  { content: '..'; }
      100% { content: '...'; }
    }

  }



  .circle-outer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 2;


    .faceCapture {
      position: absolute;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 3;
      display: flex;
      align-items: center;
      justify-content: center;

      video,
      canvas {
        width: 100vh;
        height: 100vw;
        object-fit: cover;
        transform: rotate(90deg);
        transform-origin: center;
        position: absolute;
      }

      .canvasDom {
        border-radius: 2%;
      }

      //.number {
      //  position: absolute;
      //  top: 50%;
      //  left: 50%;
      //  transform: translate(-50%, -50%);
      //  font-size: 230rem;
      //  color: white;
      //  opacity: 0.6;
      //  z-index: 4;
      //}
      //

    }
     .circle-inner {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 122%;
        height: 122%;
        object-fit: contain;
        transform: translate(-50%, -50%);
        z-index: 4;
        pointer-events: none;
      }


  }

  .title {
    position: absolute;
    top: 77%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80vw;
    font-size: 4vw;
    font-weight: 700;
    color: rgba(230, 230, 230, 1);
    font-family: "萍方 繁 正规体", sans-serif;
    z-index: 9;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    white-space: nowrap;
  }

  .btnWarp {
    z-index: 9;
    position: absolute;
    top: 82%;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 96rem;
  }

  .btnConfirm {
    width: 520rem;
    height: 120rem;
    line-height: 120rem;
    text-align: center;
    background: url("@/assets/home/<USER>");
    background-size: 100% 100%;
    font-size: 48rem;
    font-weight: bold;
    color: #ffffff;
    font-family: "思源黑体";
    border: none;
  }

  .btnRetake {
    width: 520rem;
    height: 120rem;
    line-height: 120rem;
    text-align: center;
    background: transparent;
    border: 4rem solid rgba(255, 255, 255, 0.5);
    font-size: 48rem;
    color: #ffffff;
    font-family: "思源黑体";
    font-weight: bold;
    border-radius: 999rem;
    box-sizing: border-box;
  }



  .btn, .btn2 {
    position: absolute;
    z-index: 9;
    top: 320rem;
    left: 60rem;
    width: 320rem;
    height: 120rem;
    line-height: 120rem;
    text-align: center;
    background: transparent;
    border: 4rem solid rgba(255, 255, 255, 0.5);
    font-size: 48rem;
    color: #ffffff;
    font-family: "思源黑体";
    font-weight: bold;
    border-radius: 999rem;
    box-sizing: border-box;
  }
  .btn2{
    right: 60rem;
    left: auto;
  }

  .error-toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #ff4d4f;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    z-index: 9999;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: fadeInDown 0.3s ease-out;
  }

  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translate(-50%, -20px);
    }
    to {
      opacity: 1;
      transform: translate(-50%, 0);
    }
  }


}
</style>
