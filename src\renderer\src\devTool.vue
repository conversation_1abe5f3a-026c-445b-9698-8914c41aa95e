<script>
import 'animate.css'
import bk from '@/assets/welcome/images/back_p.jpg'
import { ElMessage } from 'element-plus'
import electronApi from '@/utils/electronApi'

export default {
  data(){
    return {
      clickCount: 0,
      clickTimer: null,
      isDevMode: false,
      bk
    }
  },
  beforeCreate(){
    document.addEventListener('keydown', (event) => {
      if (event.key === 'F12') {
        electronApi.dev.toggleDevTools()
      }
    })
    if (location.href.indexOf('isTest')!=-1){
      electronApi.globals.setIsTest(true)
    }

    let client = localStorage.getItem('clientId')
    electronApi.globals.setClientId(client && JSON.parse(client))
    let token = localStorage.getItem('token')
    electronApi.globals.setToken(token)
  },
  methods: {
    toHome() {
      console.log(electronApi.globals.getClientId())
      this.$router.push({ path: '/welcome' })
    },
    closeApp() {
      electronApi.app.closeApp()
    },
    openTest() {
      // 如果是第一次点击，开始计时
      if (this.clickCount === 0) {
        this.clickTimer = setTimeout(() => {
          // 重置点击次数和计时器
          this.clickCount = 0;
          clearTimeout(this.clickTimer);
          this.clickTimer = null;
        }, 1000); // 设置间隔时间为1000毫秒
      }
 
      this.clickCount++; // 增加点击次数
 
      // 如果已经连续点击了5次，执行需要的操作
      if (this.clickCount === 5) {
        // 这里执行连续点击5次后的逻辑
        console.log('连续点击了5次！');
        ElMessage('打开了开发者模式')
        this.isDevMode = true
        // 重置点击次数和计时器
        this.clickCount = 0;
        clearTimeout(this.clickTimer);
        this.clickTimer = null;
      }
    },
    dev(type){
        if (type == 'close'){
          electronApi.app.closeApp()
        }
        if (type == 'backHome'){
          this.$router.push({ path: '/welcome' })
        }
        if (type == 'backLogin'){
          this.$router.push({ path: '/login' })
        }
        if (type == 'noTest'){
          // electronApi.globals.setIsTest(false)
          this.isDevMode = false
        }
    }
  }
}
</script>

<template>
  <div @click="toHome" class="toHome"></div>
  <div @click="closeApp" class="toHome close"></div>
  <div @click="openTest" class="toHome openTest"> </div>
  <div class="devtool" v-if="isDevMode">
    <div  @click="dev('close')">关闭程序</div>
    <div  @click="dev('backHome')">回首页</div>
    <div  @click="dev('backLogin')">回登录页</div>
    <div @click="dev('noTest')">退出开发者模式</div>
  </div>
</template>

<style scoped>
.devtool{
    position: absolute;
    left: 0;
    top: 5vw;
    z-index: 10000;
    font-size: 1.5vw;
    color: black;
    cursor: pointer;
}
.devtool >div{
    background: white;
    padding: 1vw;
    border-radius: 1vw;
    margin-bottom: 1vw;
}
.bk {
  height: 100vh;
  width: 100vw;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  /* background: url('@/assets/family/images/family-bg.svg') no-repeat center center; */
  background-size: cover;
}
.toHome {
  height: 5vw;
  width: 5vw;
  position: absolute;
  top: 0;
  left: 0;
  background-image: url('./assets/back.png');
  z-index: 9999;
}
.openTest{
  top:auto;
  left: 0;
  bottom: 0;
}
.close {
  right: 0;
  left: auto;
}
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
