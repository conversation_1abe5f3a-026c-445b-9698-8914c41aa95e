import fs from 'fs'
import path from 'path'
import { app } from 'electron'
import os from 'os'
import https from 'https'
import http from 'http'
import { URL } from 'url'

interface ConfigData {
  config_data: {
    root: Record<string, any>
  }
  config_version: string
}

interface DownloadProgress {
  url: string
  filename: string
  progress: number
  status: 'pending' | 'downloading' | 'completed' | 'error'
  error?: string
}

class ConfigResourceManager {
  private baseConfigPath: string
  private currentVersion: string | null = null
  private downloadProgress: Map<string, DownloadProgress> = new Map()

  constructor() {
    // 根据应用模式确定基础配置路径
    if (app.isPackaged) {
      const appName = app.getName()
      this.baseConfigPath = path.join(os.homedir(), 'AppData', 'Roaming', appName, 'configs')
    } else {
      this.baseConfigPath = path.join(os.homedir(), 'AppData', 'Roaming', 'popofifi-dev', 'configs')
    }
    
    // 确保基础目录存在
    this.ensureDirectoryExists(this.baseConfigPath)
  }

  /**
   * 确保目录存在
   */
  private ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true })
    }
  }

  /**
   * 获取版本配置目录路径
   */
  private getVersionPath(version: string): string {
    return path.join(this.baseConfigPath, version)
  }

  /**
   * 获取配置文件路径
   */
  private getConfigFilePath(version: string): string {
    return path.join(this.getVersionPath(version), 'config.json')
  }

  /**
   * 获取资源目录路径
   */
  private getResourcesPath(version: string): string {
    return path.join(this.getVersionPath(version), 'resources')
  }

  /**
   * 从URL下载文件
   */
  private downloadFile(url: string, filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const parsedUrl = new URL(url)
      const protocol = parsedUrl.protocol === 'https:' ? https : http
      
      const file = fs.createWriteStream(filePath)
      
      const request = protocol.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`))
          return
        }

        const totalSize = parseInt(response.headers['content-length'] || '0', 10)
        let downloadedSize = 0

        response.on('data', (chunk) => {
          downloadedSize += chunk.length
          const progress = totalSize > 0 ? (downloadedSize / totalSize) * 100 : 0
          
          // 更新下载进度
          const progressInfo = this.downloadProgress.get(url)
          if (progressInfo) {
            progressInfo.progress = progress
            progressInfo.status = 'downloading'
          }
        })

        response.pipe(file)

        file.on('finish', () => {
          file.close()
          resolve()
        })

        file.on('error', (err) => {
          fs.unlink(filePath, () => {}) // 删除不完整的文件
          reject(err)
        })
      })

      request.on('error', (err) => {
        reject(err)
      })

      request.setTimeout(30000, () => {
        request.destroy()
        reject(new Error('Download timeout'))
      })
    })
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(url: string): string {
    try {
      const parsedUrl = new URL(url)
      const pathname = parsedUrl.pathname
      const ext = path.extname(pathname)
      return ext || '.bin' // 默认扩展名
    } catch {
      return '.bin'
    }
  }

  /**
   * 生成安全的文件名
   */
  private generateSafeFilename(url: string, key: string): string {
    const ext = this.getFileExtension(url)
    // 使用配置键名作为文件名，确保唯一性
    const safeName = key.replace(/[^a-zA-Z0-9_-]/g, '_')
    return `${safeName}${ext}`
  }

  /**
   * 递归查找所有URL
   */
  private findAllUrls(obj: any, prefix: string = ''): Array<{key: string, url: string}> {
    const urls: Array<{key: string, url: string}> = []
    
    for (const [key, value] of Object.entries(obj)) {
      const currentKey = prefix ? `${prefix}.${key}` : key
      
      if (typeof value === 'string' && (value.startsWith('http://') || value.startsWith('https://'))) {
        urls.push({ key: currentKey, url: value })
      } else if (typeof value === 'object' && value !== null) {
        urls.push(...this.findAllUrls(value, currentKey))
      }
    }
    
    return urls
  }

  /**
   * 下载配置资源
   */
  async downloadConfigResources(configData: ConfigData): Promise<{success: boolean, errors: string[]}> {
    const version = configData.config_version
    const versionPath = this.getVersionPath(version)
    const resourcesPath = this.getResourcesPath(version)
    
    // 确保版本目录存在
    this.ensureDirectoryExists(versionPath)
    this.ensureDirectoryExists(resourcesPath)

    // 查找所有URL
    const urls = this.findAllUrls(configData.config_data.root)
    console.log(`Found ${urls.length} URLs to download for version ${version}`)

    const errors: string[] = []
    const localConfig = JSON.parse(JSON.stringify(configData))

    // 初始化下载进度
    urls.forEach(({key, url}) => {
      const filename = this.generateSafeFilename(url, key)
      this.downloadProgress.set(url, {
        url,
        filename,
        progress: 0,
        status: 'pending'
      })
    })

    // 下载所有资源
    for (const {key, url} of urls) {
      try {
        const filename = this.generateSafeFilename(url, key)
        const filePath = path.join(resourcesPath, filename)
        
        console.log(`Downloading: ${url} -> ${filename}`)
        
        const progressInfo = this.downloadProgress.get(url)!
        progressInfo.status = 'downloading'
        
        await this.downloadFile(url, filePath)
        
        // 验证文件是否下载完整
        if (!fs.existsSync(filePath) || fs.statSync(filePath).size === 0) {
          throw new Error('Downloaded file is empty or corrupted')
        }
        
        progressInfo.status = 'completed'
        progressInfo.progress = 100
        
        // 更新配置中的URL为本地路径
        this.updateConfigUrl(localConfig.config_data.root, key, `/config-resources/${version}/${filename}`)
        
      } catch (error) {
        const errorMsg = `Failed to download ${url}: ${error instanceof Error ? error.message : String(error)}`
        console.error(errorMsg)
        errors.push(errorMsg)
        
        const progressInfo = this.downloadProgress.get(url)!
        progressInfo.status = 'error'
        progressInfo.error = errorMsg
      }
    }

    // 保存本地配置文件
    const configFilePath = this.getConfigFilePath(version)
    fs.writeFileSync(configFilePath, JSON.stringify(localConfig, null, 2))
    
    this.currentVersion = version
    console.log(`Config resources downloaded for version ${version}. Errors: ${errors.length}`)
    
    return {
      success: errors.length === 0,
      errors
    }
  }

  /**
   * 更新配置中的URL
   */
  private updateConfigUrl(obj: any, keyPath: string, newUrl: string): void {
    const keys = keyPath.split('.')
    let current = obj
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (current[keys[i]] === undefined) {
        return
      }
      current = current[keys[i]]
    }
    
    const lastKey = keys[keys.length - 1]
    if (current[lastKey] !== undefined) {
      current[lastKey] = newUrl
    }
  }

  /**
   * 获取本地配置
   */
  getLocalConfig(version?: string): any | null {
    const targetVersion = version || this.currentVersion
    if (!targetVersion) {
      return null
    }

    const configFilePath = this.getConfigFilePath(targetVersion)
    if (!fs.existsSync(configFilePath)) {
      return null
    }

    try {
      const configData = fs.readFileSync(configFilePath, 'utf-8')
      return JSON.parse(configData)
    } catch (error) {
      console.error('Error reading local config:', error)
      return null
    }
  }

  /**
   * 获取资源文件路径
   */
  getResourcePath(version: string, filename: string): string | null {
    const filePath = path.join(this.getResourcesPath(version), filename)
    return fs.existsSync(filePath) ? filePath : null
  }

  /**
   * 获取所有可用版本
   */
  getAvailableVersions(): string[] {
    if (!fs.existsSync(this.baseConfigPath)) {
      return []
    }

    return fs.readdirSync(this.baseConfigPath)
      .filter(item => fs.statSync(path.join(this.baseConfigPath, item)).isDirectory())
      .sort()
  }

  /**
   * 获取下载进度
   */
  getDownloadProgress(): DownloadProgress[] {
    return Array.from(this.downloadProgress.values())
  }

  /**
   * 清理旧版本
   */
  cleanupOldVersions(keepCount: number = 3): void {
    const versions = this.getAvailableVersions()
    if (versions.length <= keepCount) {
      return
    }

    const versionsToDelete = versions.slice(0, versions.length - keepCount)
    
    for (const version of versionsToDelete) {
      try {
        const versionPath = this.getVersionPath(version)
        fs.rmSync(versionPath, { recursive: true, force: true })
        console.log(`Cleaned up old version: ${version}`)
      } catch (error) {
        console.error(`Failed to cleanup version ${version}:`, error)
      }
    }
  }

  /**
   * 获取当前版本
   */
  getCurrentVersion(): string | null {
    return this.currentVersion
  }

  /**
   * 设置当前版本
   */
  setCurrentVersion(version: string): void {
    this.currentVersion = version
  }
}

// 单例实例
const configResourceManager = new ConfigResourceManager()

export default configResourceManager
export { ConfigResourceManager, DownloadProgress }
