# Electron API 统一入口集成指南

## 概述

本项目已将所有 Electron API 调用统一到一个入口文件 `src/renderer/src/utils/electronApi.js`，并通过主进程的 3030 HTTP 服务中继 API 功能，使得在开发时可以同时使用 Electron 和浏览器进行开发调试。

## 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   浏览器环境     │    │   Electron环境   │    │   主进程服务     │
│                │    │                │    │                │
│  electronApi   │────│  electronApi   │────│  HTTP Server   │
│  (HTTP请求)    │    │  (直接调用)     │    │  (3030端口)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 文件结构

```
├── src/renderer/src/utils/electronApi.js    # 统一的 Electron API 入口
├── src/main/services/httpService.ts         # 主进程 HTTP 服务（包含 API 中继）
├── docs/                                   # 文档目录
│   ├── electron-api-integration.md         # 本文档
│   └── electron-api-usage.md              # 使用指南
└── src/renderer/src/examples/              # 示例文件
    └── electronApiExample.vue             # 完整使用示例
```

## 主要特性

### 1. 环境自动检测

API 会自动检测当前运行环境：
- **Electron 环境**：直接调用原生 `window.electron` 和 `window.api`
- **浏览器环境**：通过 HTTP 请求调用主进程的 3030 服务

### 2. 统一的 API 接口

所有 Electron API 调用都通过 `electronApi` 对象进行：

```javascript
import electronApi from '@/utils/electronApi'

// 应用控制
electronApi.app.closeApp()
electronApi.app.restartApp()

// 配置管理
const config = await electronApi.config.getConfig()

// 设备信息
const machineInfo = await electronApi.device.getMachineInfo()

// 全局变量管理
electronApi.globals.setIsTest(true)
const clientId = electronApi.globals.getClientId()
```

### 3. 主进程 HTTP 服务中继

主进程在 3030 端口启动 HTTP 服务，提供以下 API 端点：

#### 应用控制
- `POST /api/app/close` - 关闭应用
- `POST /api/app/restart` - 重启应用
- `GET /api/app/path` - 获取应用路径
- `GET /api/app/dev` - 获取开发环境信息

#### 配置管理
- `GET /api/config/get` - 获取配置文件
- `POST /api/config/set` - 设置本地配置
- `POST /api/config/local` - 获取本地配置
- `POST /api/config/download` - 下载配置资源
- `GET /api/config/versions` - 获取可用版本列表
- `POST /api/config/cleanup` - 清理旧版本
- `POST /api/config/version/set` - 设置当前版本

#### 设备信息
- `GET /api/device/machine-info` - 获取机器信息

#### 版本管理
- `GET /api/version/current` - 获取当前版本
- `GET /api/version/info` - 获取版本信息

#### 更新功能
- `GET /api/update/check` - 检查更新
- `POST /api/update/download` - 下载更新
- `POST /api/update/install` - 安装更新
- `GET /api/update/progress` - 获取下载进度

#### 开发者工具
- `POST /api/dev/toggle-devtools` - 切换开发者工具

#### 快捷键
- `POST /api/shortcut/execute` - 执行快捷键操作

## 已迁移的文件

以下文件已经迁移到使用统一 API：

### 核心文件
- `src/renderer/src/main.ts` ✅
- `src/renderer/src/App.vue` ✅
- `src/renderer/src/App_shishahai_yinyuejie.vue` ✅
- `src/renderer/src/devTool.vue` ✅

### 组件文件
- `src/renderer/src/components/VersionInfo.vue` ✅
- `src/renderer/src/components/UpdateChecker.vue` ✅

### 页面文件
- `src/renderer/src/views/popfifi/login.vue` ✅ (已修复导入)
- `src/renderer/src/views/shishahai_yinyuejie/login.vue` ✅ (已修复导入)
- `src/renderer/src/views/popfifi/welcome.vue` ✅
- `src/renderer/src/views/popfifi/family/index.vue` ✅
- `src/renderer/src/views/popfifi/family/result.vue` ✅
- `src/renderer/src/views/popfifi/capture.vue` ✅ (已修复导入)
- `src/renderer/src/views/shishahai_yinyuejie/capture.vue` ✅ (已修复导入)
- `src/renderer/src/views/shishahai_yinyuejie/family/result.vue` ✅
- `src/renderer/src/views/shishahai_yinyuejie/result_qrcode.vue` ✅
- `src/renderer/src/views/shishahai_yinyuejie/loading.vue` ✅

### 工具文件
- `src/renderer/src/store/config/index.js` ✅
- `src/renderer/src/utils/configManager.ts` ✅ (已修复导入)

### 验证工具
- `scripts/verify-electronapi-imports.js` ✅ (新增验证脚本)

## 开发模式

### Electron 开发（推荐）
```bash
npm run dev
```

### 浏览器开发（调试用）
1. 启动 Electron 应用（主进程服务会自动启动）
```bash
npm run dev
```

2. 在浏览器中访问渲染进程
```
http://localhost:5173
```

这样可以同时在 Electron 和浏览器中进行开发调试。

## 向后兼容

为了保持向后兼容，统一 API 提供了原始 API 的访问方式：

```javascript
// 直接访问原始 API（仅在 Electron 环境下可用）
const rawElectron = electronApi.raw.electron
const rawApi = electronApi.raw.api

// 环境检测
if (electronApi.isElectron()) {
  // Electron 环境特定代码
}
```

## 注意事项

1. **异步调用**：所有 API 调用都是异步的，请使用 `await` 或 `.then()`
2. **错误处理**：建议为所有 API 调用添加错误处理
3. **服务依赖**：浏览器环境下需要主进程的 HTTP 服务运行
4. **端口占用**：确保 3030 端口未被其他服务占用

## 扩展指南

如需添加新的 API，请：

1. 在 `src/main/services/httpService.ts` 中添加对应的路由
2. 在 `src/renderer/src/utils/electronApi.js` 中添加对应的方法
3. 更新文档和示例

## 故障排除

### 常见问题

1. **`electronApi is not defined` 错误**
   - **原因**: 文件中使用了 `electronApi` 但没有正确导入
   - **解决方案**: 在文件顶部添加导入语句
   ```javascript
   import electronApi from '@/utils/electronApi'
   ```
   - **检查方法**: 确保每个使用 `electronApi` 的文件都有正确的导入语句

2. **API 调用失败**
   - 检查主进程 HTTP 服务是否正常运行
   - 确认端口 3030 未被占用
   - 查看浏览器控制台和主进程日志

3. **环境检测错误**
   - 确保正确导入 `electronApi`
   - 检查 `window.electron` 和 `window.api` 是否正确暴露

4. **类型错误**
   - 确保使用正确的 API 方法名
   - 检查参数格式是否正确

5. **模块路径错误**
   - 确保路径别名 `@` 正确配置
   - 检查 `electronApi.js` 文件是否存在于正确位置

### 调试技巧

1. **验证导入**
   ```javascript
   // 在浏览器控制台中测试
   import('@/utils/electronApi').then(api => {
     console.log('API 加载成功:', api.default)
   }).catch(err => {
     console.error('API 加载失败:', err)
   })
   ```

2. **测试 API 功能**
   ```javascript
   // 使用测试工具
   import { testElectronApi } from '@/utils/electronApiTest'
   testElectronApi()
   ```

3. **检查网络请求**（浏览器环境）
   - 打开浏览器开发者工具
   - 查看 Network 标签页
   - 确认对 `http://localhost:3030` 的请求是否成功

4. 使用浏览器开发者工具查看网络请求
5. 检查主进程控制台日志
6. 使用 `electronApi.isElectron()` 确认当前环境

## 性能考虑

1. **缓存机制**：全局变量在浏览器环境下使用 localStorage 缓存
2. **请求优化**：HTTP 请求使用 JSON 格式，减少数据传输
3. **错误恢复**：提供合理的错误处理和重试机制

## 安全考虑

1. **端口限制**：HTTP 服务仅监听本地 3030 端口
2. **CORS 配置**：已配置 CORS 允许本地开发
3. **数据验证**：对传入参数进行基本验证

## 总结

通过统一的 Electron API 入口和主进程 HTTP 服务中继，实现了：

- **开发效率提升**：可以在浏览器环境下进行开发调试
- **代码维护性**：统一的 API 接口，便于管理和维护
- **环境兼容性**：自动适配 Electron 和浏览器环境
- **向后兼容**：保留原有 API 访问方式

这种架构设计既保证了开发的灵活性，又维持了代码的一致性和可维护性。
