<template>
  <div class="family">
    <div class="top">
      <div class="logo-content">
       <img class="my-logo" src="@/assets/hehua/logo.png" alt="" v-if="!isTest" />
       <!-- 测试模式下的上传按钮 -->
        <div v-if="isTest" class="test-upload-btn">
          <button @click="handleFileUpload" :disabled="fileUploadLoading" class="upload-button">
            {{ fileUploadLoading ? '上传中...' : '选择图片' }}
          </button>
        </div>
      </div>
      <div class="outer">
        <div
          v-show="!isShowCapturedImage"
          class="camera-content"
          ref="cameraContent"
          id="cameraContent"
        >
          <div class="my-video">
            <!-- <video ref="myVideo" width="100%" height="100%" autoplay playsinline></video> -->
            <!-- 加入镜像反转 -->

            <base-video
              :extraStyle="{
                transform: 'scaleX(-1)  scale(1) '
              }"
              @initFinished="handleInitFinished"
            ></base-video>
            <img class="my-img" :src="canvasImg" />
          </div>
          <!-- <div class="last-img-content">
          <img class="last-img" :src="capturedImage" alt="" ref="imgDom" />
        </div> -->
        </div>
        <div v-show="isShowCapturedImage" class="show-image-content">
          <img class="show-image" :src="capturedImage" alt="" ref="imgDom" />
          <div class="box-inner">
            <camera-focus-box
              v-for="(boxItem, boxIndex) in boxItems"
              :key="boxIndex"
              :boxStyle="{
                ...boxItem
              }"
              @click="handleClickBoxItem(boxItem)"
            >
            </camera-focus-box>
          </div>
          <div class="setting-content" v-if="selectedBox.length">
            <div v-for="selectedItem in selectedBox" :key="selectedItem.id" class="setting-item">
              <div class="setting-item-box1" @click="handleClickGenderReverse(selectedItem)">
                <div class="setting-item-box1-inner">{{ sexType[selectedItem.gender] }}</div>
              </div>
              <div class="setting-item-box2" @click="handleClickAgeReverse(selectedItem)">
                {{ ageType[selectedItem.ageType] }}
              </div>
            </div>
          </div>
        </div>
        <div v-if="countdown != 0" class="count-numer">
          {{ countdown }}
        </div>
        <div v-show="shibieLoading" class="loading">
          <base-loading :isInitBoxWidthAndHeight="true" size="80rem"></base-loading>
        </div>
      </div>
    </div>

    <!-- <div class="text-btn" @click="handleClickShibei">识别</div>
      <div class="text-btn" @click="handleReset">重置</div> -->
    <!-- <div ref="imgRes"></div> -->

    <activity-bottom
      goodCode="V1_FAMILY"
      :countdown="isTest?0:5"
      :isHasBuyBtn="isHasBuyBtn"
      :isHasSureBtn="isHasSureBtn"
       :themeImg="{
        bgImg:bgBottom,
        btnImg:btnBg,
        outerCircleBgImg:btnWraper,
        reTakeBtnTextImg:retakeText,
        backBtnTextImg:backText,
        sureBtnTextImg:sureText,
      }"
      @countDecrease="handleCountDecrease"
      @countFinished="handleCountFinished"
      @handleSure="handleReceiveSure"
      @handleRetake="handleReceiveRetake"
      @handleBack="handleReceiveBack"
    >
    </activity-bottom>
    <!-- <div class="test-img">
      <img v-for="item in testImgData" :key="item.id" :src="item.thumbnail" alt="">
    </div> -->
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import { getImageSize, base64ToBlob } from '@/utils'
import CameraFocusBox from '@/components/base/CameraFocusBox/index.vue'
import { snapdom } from '@zumer/snapdom'
import ActivityBottom from '@/components/activity/Bottom/index.vue'
import bus from '@/utils/bus'
import BaseLoading from '@/components/base/Loading/index.vue'
import { calculateFaceQuality } from '@/utils/faceComputedFn'
import { generateUUID } from '@/utils/uuid'
import { useFamilyPhotoStore } from '@/store/family'
import { getPhotoFaceSexAndAge } from '@/apis/face'
import BaseVideo from '@/components/base/Video/index.vue'
import bgBottom from  '@/assets/hehua/family/bg_bottom.png'
import btnBg from  '@/assets/hehua/family/btn-bg.png'
import btnWraper from  '@/assets/hehua/family/btn-wraper.png'
import retakeText from  '@/assets/hehua/family/retake-text.png'
import backText from  '@/assets/hehua/family/back-text.png'
import sureText from  '@/assets/hehua/family/sure-text.png'
import electronApi from '@/utils/electronApi'

export default {
  components: {
    CameraFocusBox,
    ActivityBottom,
    BaseLoading,
    BaseVideo
  },
  data() {
    return {
      isTest: electronApi.globals.getIsTest(),
      canvasImg: '',
      capturedImage: '',
      boxItems: [],
      myCanvas: null,
      isShowCapturedImage: false,
      countdown: 0,
      shibieLoading: false,
      isHasBuyBtn: false,
      isHasSureBtn: false,
      sexType: {
        male: '先生',
        female: '女士'
      },
      ageType: {
        small: '小',
        big: '大'
      },
      testImgData: [],
      bottomBgColor: 'rgba(0,0,0,0)',
      commonVideoRef: null,
      commonCameraInited: false,
      bgBottom,
      btnBg,
      btnWraper,
      retakeText,
      backText,
      sureText,
    }
  },
  watch: {
    commonCameraInited(newVal) {
      if (newVal) {
        bus.emit('cameraInitFinished')
      }
    }
  },
  // 添加组件销毁钩子
  computed: {
    selectedBox() {
      // 按照left的顺序正序
      const filterArr = this.boxItems.filter((item) => item.isSelected)
      if (filterArr.length) {
        this.bottomBgColor = 'rgba(0,0,0,0.2)'
      } else {
        this.bottomBgColor = 'rgba(0,0,0,0)'
      }
      return filterArr.sort((a, b) => a.left - b.left)
    }
  },
  methods: {
    // 收到basevideo初始化完成的回调
    handleInitFinished(videoRef) {
      console.log('加载完')
      this.commonVideoRef = videoRef
      this.myCanvas = document.createElement('canvas')
      this.commonCameraInited = true
    },

    // 新增拍照方法
    capturePhoto() {
      let that = this
      const video = this.commonVideoRef
      video.pause()

      const canvas = this.myCanvas || document.createElement('canvas')
      // canvas.width = video.videoHeight
      // canvas.height = video.videoWidth
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight
      const ctx = canvas.getContext('2d')
      ctx.save()
      ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight)
      ctx.restore() // 恢复画布状态
      // 保存base64图片数据
      this.canvasImg = canvas.toDataURL('image/jpeg')
      const targetElement = document.getElementById('cameraContent')

      return new Promise(async (resolve, reject) => {
        // // 获取原始尺寸
        const originalWidth = targetElement.offsetWidth
        const originalHeight = targetElement.offsetHeight
        // 设置最大尺寸
        const maxSize = 1400
        let scale = 1
        // 计算缩放比例
        if (originalWidth > maxSize || originalHeight > maxSize) {
          scale = Math.min(maxSize / originalWidth, maxSize / originalHeight)
        }
        this.$nextTick(async () => {
          const img = await snapdom.toJpg(targetElement, { dpr: scale })
          that.isShowCapturedImage = true
          that.capturedImage = img.src
          resolve(img.src)
        })
      })
    },

    async handleClickShibei() {
      this.shibieLoading = true
      let that = this
      if (electronApi.globals.getIsTest()){
        console.log('tt')
        await this.handleFileUpload()
      }else{
        const res = await this.capturePhoto()
        console.log(res, '这是res')
      }
      
      const imgInfo = await getImageSize(this.capturedImage)
      const scale = this.$refs.imgDom.width / imgInfo.width
      console.log(this.$refs.imgDom.width, imgInfo.width, scale, 'scale.value')
      const detections = await getPhotoFaceSexAndAge({ base64: this.capturedImage })
      this.handleDections(detections, scale)
    },

    handleDections(detections, scale) {
      console.log(!detections || !detections.length, 'shenme')
      if (!detections || !detections.length) {
        this.shibieLoading = false
        ElMessage.error('未检测到人脸,请重试!')
        return false
      }
      const sorted = detections.map((d) => ({
        ...d,
        imageHeight: d.alignedRect._imageDims._height,
        imageWidth: d.alignedRect._imageDims._width,
        area: d.detection._box._width * d.detection._box._height,
        score: d.detection._score,
        quality: calculateFaceQuality(d.landmarks)
      }))

      //  取眼睛清晰度>30的，score>20, 面积1280*720 的2万
      const filterArr = sorted.filter((sitem) => {
        console.log(sitem, 'guo')
        const filterArea = ((sitem.imageHeight * sitem.imageWidth) / (1280 * 720)) * 7000
        if (sitem.score * 100 > 30 && sitem.quality.score > 30 && sitem.area > filterArea) {
          return sitem
        }
      })

      if (!filterArr.length) {
        this.shibieLoading = false
        ElMessage.error('未检测到人脸,请重试!')
        return false
      }

      this.boxItems = filterArr.map((item) => {
        console.log(item, 'item')
        return {
          id: generateUUID(),
          width: item.alignedRect._box._width * scale,
          height: item.alignedRect._box._height * scale,
          top: item.alignedRect._box._y * scale,
          left: item.alignedRect._box._x * scale,
          isSelected: false,
          imageHeight: item.imageHeight,
          imageWidth: item.imageWidth,
          age: item.age,
          ageType: item.age > 13 ? 'big' : 'small',
          gender: item.gender
        }
      })
      this.shibieLoading = false
      this.isHasSureBtn = true
      bus.emit('cameraCaptureFinished')
    },
    handleReset() {
      this.commonVideoRef.play()
      this.boxItems = []
      this.isShowCapturedImage = false
    },
    handleClickBoxItem(item) {
      item.isSelected = !item.isSelected
    },
    handleCountDecrease(count) {
      this.countdown = count
    },
    handleCountFinished() {
      this.handleClickShibei()
    },
    handleReceiveSure() {
      let that = this
      // 如果没有选择，则弹出提示选择后再继续
      if (!this.selectedBox.length) {
        ElMessage.error('请选择人像后再继续!')
        return false
      }
      // 开始截人脸图
      // 开始组装数据
      const faces = this.selectedBox.map(async (item) => {
        const faceData = await this.captureFaceImage({
          left: item.left,
          top: item.top,
          width: item.width,
          height: item.height
        })
        const file = new File([base64ToBlob(faceData)], `${item.id}.jpg`, { type: 'image/jpeg' })
        return {
          id: item.id,
          thumbnail: faceData,
          thumbnailFile: file,
          ageType: item.ageType,
          gender: item.gender
        }
      })

      Promise.all(faces).then((results) => {
        // 这里可以拿到所有裁剪后的图片数据
        console.log('所有裁剪结果:', results)
        // that.testImgData = results;
        const useFamilyStore = useFamilyPhotoStore()
        useFamilyStore.setFamilyPhotoData(results)
        const file = new File([base64ToBlob(that.canvasImg)], `${generateUUID()}.jpg`, {
          type: 'image/jpeg'
        })
        const params = {
          oriBase64: that.canvasImg,
          orifile: file
        }
        useFamilyStore.setFamilyOriPic(params)
        console.log(useFamilyStore, 'familyStore')

        // 存储到store中

        // 后续处理逻辑...
        // 跳转到下个路由
        this.$router.push({ path: '/family-loading' })
      })
    },
    handleReceiveRetake() {
      this.initParams()
      //  重置相机
      this.commonVideoRef.play()
    },
    handleReceiveBack() {
      this.$router.push({ path: '/welcome' })
    },
    handleClickGenderReverse(item) {
      const findIndex = this.boxItems.findIndex((fItem) => fItem.id == item.id)
      let gender = ''
      if (findIndex != -1) {
        item.gender == 'male' && (gender = 'female')
        item.gender == 'female' && (gender = 'male')
        this.boxItems[findIndex] = {
          ...item,
          gender
        }
      }
    },
    handleClickAgeReverse(item) {
      const findIndex = this.boxItems.findIndex((fItem) => fItem.id == item.id)
      let ageType = ''
      if (findIndex != -1) {
        item.ageType == 'small' && (ageType = 'big')
        item.ageType == 'big' && (ageType = 'small')
        this.boxItems[findIndex] = {
          ...item,
          ageType
        }
      }
    },

    async captureFaceImage(faceBox) {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      return new Promise((resolve) => {
        img.onload = () => {
          // 使用原始图像尺寸计算
          const originalWidth = img.naturalWidth
          const originalHeight = img.naturalHeight

          // 计算实际缩放比例（显示尺寸/原始尺寸）
          const displayScale = this.$refs.imgDom.width / originalWidth

          // 将显示坐标转换为原始图像坐标
          const originLeft = faceBox.left / displayScale
          const originTop = faceBox.top / displayScale
          const originWidth = faceBox.width / displayScale
          const originHeight = faceBox.height / displayScale

          // 应用1.3倍放大
          const scaledWidth = originWidth 
          const scaledHeight = originHeight 
          const scaledLeft = originLeft - (scaledWidth - originWidth) / 2
          const scaledTop = originTop - (scaledHeight - originHeight) / 2

          // 边界检查
          const finalLeft = Math.max(0, scaledLeft)
          const finalTop = Math.max(0, scaledTop)
          const finalRight = Math.min(originalWidth, scaledLeft + scaledWidth)
          const finalBottom = Math.min(originalHeight, scaledTop + scaledHeight)
          const finalWidth = finalRight - finalLeft
          const finalHeight = finalBottom - finalTop

          canvas.width = finalWidth
          canvas.height = finalHeight

          ctx.drawImage(
            img,
            finalLeft,
            finalTop,
            finalWidth,
            finalHeight,
            0,
            0,
            finalWidth,
            finalHeight
          )
          resolve(canvas.toDataURL('image/jpeg'))
        }
        img.src = this.capturedImage // 使用原始图像数据
      })
    },

    initParams() {
      this.isShowCapturedImage = false
      this.canvasImg = ''
      this.capturedImage = ''
      this.boxItems = []
      this.shibieLoading = false
      this.isHasBuyBtn = false
      this.isHasSureBtn = false
    },

    // 文件上传处理方法
    async handleFileUpload() {
      this.fileUploadLoading = true
      let that = this
      return new Promise((resolve, reject) => {
        // 创建隐藏的文件输入元素
        const fileInput = document.createElement('input')
        fileInput.type = 'file'
        fileInput.accept = 'image/*'
        fileInput.style.display = 'none'

        fileInput.onchange = async (event) => {
          const file = event.target.files[0]
          if (!file) {
            this.fileUploadLoading = false
            reject(new Error('未选择文件'))
            return
          }

          // 验证文件类型
          if (!file.type.startsWith('image/')) {
            this.fileUploadLoading = false
            ElMessage.error('请选择图片文件')
            reject(new Error('文件类型不正确'))
            return
          }

          // 验证文件大小 (限制为10MB)
          const maxSize = 10 * 1024 * 1024
          if (file.size > maxSize) {
            this.fileUploadLoading = false
            ElMessage.error('文件大小不能超过10MB')
            reject(new Error('文件过大'))
            return
          }

          try {
            // 使用FileReader读取文件并转换为base64
            const reader = new FileReader()
            reader.onload = (e) => {
              const imageData = e.target.result
              this.capturedImage = imageData
              this.fileUploadLoading = false

              // 隐藏video元素，显示上传的图片
              // this.hideVideoShowImage()

              console.log('文件上传成功，已设置capturedImage')
              ElMessage.success('图片上传成功')
              that.isShowCapturedImage = true
              that.canvasImg = imageData
              resolve(imageData)
            }
            reader.onerror = () => {
              this.fileUploadLoading = false
              ElMessage.error('文件读取失败')
              reject(new Error('文件读取失败'))
            }
            reader.readAsDataURL(file)
          } catch (error) {
            this.fileUploadLoading = false
            console.error('文件处理错误:', error)
            ElMessage.error('文件处理失败')
            reject(error)
          } finally {
            // 清理DOM元素
            if (document.body.contains(fileInput)) {
              document.body.removeChild(fileInput)
            }
          }
        }

        fileInput.oncancel = () => {
          this.fileUploadLoading = false
          if (document.body.contains(fileInput)) {
            document.body.removeChild(fileInput)
          }
          reject(new Error('用户取消选择文件'))
        }

        // 添加到DOM并触发点击
        document.body.appendChild(fileInput)
        fileInput.click()
      })
    },

  }
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: 'DouyinSansBold';
  src: url('@/assets/fonts/DouyinSansBold.ttf') format('truetype');
}
.family {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: 'DouyinSansBold', sans-serif;
  // background: url('@/assets/family/images/family-bg.svg') no-repeat center center; // 新增背景图
  // background-size: cover; // 保持背景图覆盖
  .top {
    height: 80%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    .logo-content {
      width: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 90;
      padding-top: 10rem;
      .my-logo {
        width: 80%;
        height: auto;
      }
    }
    .outer {
      flex: 1;
      width: 100%;
      position: relative;
      .camera-content {
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;
        .my-video {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          .my-img {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 1;
            transform: scaleX(-1)  scale(1);
            // 加入镜像左右翻转
          }
        }
        .last-img-content {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          z-index: 2;
          .last-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
      .show-image-content {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        .show-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .box-inner {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 2;
        }
        .setting-content {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 13%;
          // background: rgba(0, 0, 0, 0.2);
          // border-radius: 226rem 226rem 0 0;
          display: flex;

          justify-content: space-around;
          align-items: center;
          .setting-item {
            width: 312rem; //18.7%
            height: 173rem;
            background-color: transparent;
            position: relative;
            font-size: 52.17rem;
            font-weight: 700;
            color: #fff;
            &-box1 {
              left: 0;
              top: 0;
              width: 173.8rem;
              height: 173.8rem;
              border-radius: 50%;
              background: linear-gradient(90deg, #5be499 0%, rgb(206, 226, 202) 100%);
              position: absolute;
              display: flex;
              justify-content: center;
              align-items: center;
              z-index: 10;
              &-inner {
                position: absolute;
                width: 86%;
                height: 86%;
                border: 1.6rem dashed #fff;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }
            &-box2 {
              right: 17rem;
              margin-top: 17rem;
              width: 138.4rem;
              height: 138.4rem;
              border-radius: 50%;
              background: linear-gradient(90deg, rgb(206, 226, 202) 0%, rgb(223, 165, 220) 100%);
              position: absolute;
              z-index: 9;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
      }
      .count-numer {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 507rem;
        color: #fff;
        font-weight: 700;
      }
      .loading {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 999;
        // 新增毛玻璃效果
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px); // 兼容Safari
      }
    }
  }
  .bottom {
    flex: 1;
    .text-btn {
      width: 160rem;
      height: 80rem;
      font-size: 40rem;
    }
  }
  .test-img {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 50%;
    }
  }
}
</style>
