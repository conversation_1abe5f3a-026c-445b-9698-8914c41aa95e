import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import electronApi from '@/utils/electronApi'

import {init} from './initConfig'

const config = ref({})
const name = ref({})
const pwd = ref({})

const setPwd = (data) => {
  pwd.value = data
}
const setConfig = (data) => {
  config.value = data
}
const setName = (data) => {
  name.value = data
}

const loadConfig = () => {
  // 判断当前的环境是浏览器环境还是应用环境
  // 根据不同的环境选择不同的做法
  // 统一使用 electronApi 获取配置，自动适配环境
  return new Promise(async (resolve, reject) => {
    try {
      // 使用统一的 electronApi 获取配置
      const config = await electronApi.config.getConfig()

      if (config && config.APP) {
        const appConfig = config.APP
        setConfig(appConfig)
        setName(appConfig.name)

        if (appConfig.pwd) {
          // 如果密码存在的话
          setPwd(appConfig.pwd)
        } else {
          // 如果密码不存在的话，就取用生成的机器码
          const machineInfo = await electronApi.device.getMachineInfo()
          setPwd(machineInfo.machineId)
        }
      } else {
        // 如果没有获取到配置，使用默认配置
        console.warn('未获取到配置，使用默认配置')
        const defaultAppConfig = {
          style: "",
          alwaysOnTop: false,
          fullScreen: false,
          name: "",
          pwd: ""
        }
        setConfig(defaultAppConfig)
        setName(defaultAppConfig.name)

        // 获取机器码作为默认密码
        try {
          const machineInfo = await electronApi.device.getMachineInfo()
          setPwd(machineInfo.machineId)
        } catch (machineError) {
          console.warn('无法获取机器信息，使用默认密码')
          setPwd('default')
        }
      }

      resolve(true)
    } catch (error) {
      console.error('读取配置文件失败:', error)
      ElMessage.error('读取配置文件失败')
      reject(error)
    }
  })
}

// 配置相关
export const useConfigStore = defineStore('config', () => {
  return { config, name, pwd, loadConfig, setConfig, init }
})
