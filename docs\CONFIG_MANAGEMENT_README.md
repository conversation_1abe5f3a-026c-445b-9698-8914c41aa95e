# 配置资源管理系统

这个系统实现了配置文件的本地缓存和资源下载功能，确保开发环境、测试环境和生产环境的一致性。

## 功能特性

- ✅ 自动下载远程配置中的所有URL资源到本地
- ✅ 根据配置版本创建独立的存储目录
- ✅ 通过HTTP服务提供本地资源访问
- ✅ 支持下载进度监控和错误处理
- ✅ 版本管理和旧版本清理
- ✅ 开发/测试/生产环境隔离
- ✅ 离线使用支持
- ✅ **智能版本比较和更新提醒**
- ✅ **优先使用本地配置，API版本更新时提示用户**

## 目录结构

```
%APPDATA%/Roaming/
├── popofifi-dev/configs/          # 开发环境
├── popofifi-test/configs/         # 测试环境
└── popofifi-prod/configs/         # 生产环境
    ├── v1.0.22/                   # 版本目录
    │   ├── config.json            # 本地化配置文件
    │   └── resources/             # 资源文件目录
    │       ├── common_background.png
    │       ├── common_logo.png
    │       └── ...
    └── v1.0.23/
        ├── config.json
        └── resources/
```

## 使用方法

### 1. 基本使用

```javascript
import configHelper from '@/utils/configHelper'

// 获取配置值
const backgroundUrl = configHelper.getBackgroundUrl()
const logoUrl = configHelper.getLogoUrl()

// 获取相机按钮配置
const cameraButtons = configHelper.getCameraButtonConfig()
const backButtonUrl = configHelper.getCameraButtonUrl('back')
```

### 2. 在Vue组件中使用

```vue
<template>
  <div>
    <img :src="backgroundUrl" alt="背景" />
    <img :src="logoUrl" alt="Logo" />
    
    <div class="camera-buttons">
      <img 
        v-for="(url, name) in cameraButtons" 
        :key="name"
        :src="url" 
        :alt="name"
        @click="handleButtonClick(name)"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import configHelper from '@/utils/configHelper'

const backgroundUrl = computed(() => configHelper.getBackgroundUrl())
const logoUrl = computed(() => configHelper.getLogoUrl())
const cameraButtons = computed(() => configHelper.getCameraButtonConfig())

const handleButtonClick = (buttonName) => {
  console.log(`点击了${buttonName}按钮`)
}
</script>
```

### 3. 响应式配置使用

```javascript
import { useConfig, useResourceUrl } from '@/utils/configHelper'

// 在setup中使用
const backgroundUrl = useResourceUrl('common.background')
const someConfig = useConfig('common.someProperty', 'defaultValue')
```

### 4. 版本检查和更新

```javascript
import { checkForUpdates, updateConfig } from '@/utils/configHelper'

// 检查是否有新版本
const updateInfo = await checkForUpdates()
if (updateInfo.hasUpdate) {
  console.log(`发现新版本: ${updateInfo.apiVersion}，当前版本: ${updateInfo.localVersion}`)

  // 手动更新配置
  const result = await updateConfig()
  if (result.success) {
    console.log(result.message)
  }
}
```

### 5. 手动下载配置

```javascript
import configManager from '@/utils/configManager'

// 从API获取配置并下载资源
const response = await axios.get('/api/v1.0/device/config')
await configManager.downloadConfigResources(response.data.data)

// 或使用助手函数
import { refreshConfig } from '@/utils/configHelper'
await refreshConfig()
```

## API接口

### HTTP服务接口 (localhost:3030)

- `POST /download-config-resources` - 下载配置资源
- `GET /local-config/:version?` - 获取本地配置
- `GET /download-progress` - 获取下载进度
- `GET /available-versions` - 获取可用版本列表
- `GET /config-resources/:version/:filename` - 访问静态资源

### IPC接口

- `download-config-resources` - 下载配置资源
- `get-local-config` - 获取本地配置
- `get-download-progress` - 获取下载进度
- `get-available-versions` - 获取可用版本
- `cleanup-old-versions` - 清理旧版本
- `set-current-version` - 设置当前版本

## 配置文件格式

```json
{
  "config_data": {
    "root": {
      "common": {
        "background": "https://example.com/bg.png",
        "logo": "https://example.com/logo.png",
        "cameraBtnGroup": {
          "back": "https://example.com/back.png",
          "buy": "https://example.com/buy.png",
          "check": "https://example.com/check.png",
          "retake": "https://example.com/retake.png"
        }
      }
    }
  },
  "config_version": "v1.0.23"
}
```

下载后，URL会被替换为本地路径：

```json
{
  "config_data": {
    "root": {
      "common": {
        "background": "/config-resources/v1.0.23/common_background.png",
        "logo": "/config-resources/v1.0.23/common_logo.png"
      }
    }
  },
  "config_version": "v1.0.23"
}
```

## 错误处理

系统提供完善的错误处理机制：

1. **下载失败重试** - 用户可以手动重试失败的下载
2. **部分下载失败** - 系统会报告哪些资源下载失败
3. **网络异常** - 自动回退到本地缓存配置
4. **文件损坏检测** - 验证下载文件的完整性

## 测试页面

访问 `/config-test` 路由可以看到配置管理的测试界面，包括：

- 配置下载和进度监控
- 版本管理
- 配置预览
- API测试

## 版本比较逻辑

系统采用智能的版本比较策略：

### 启动时的版本检查流程

1. **优先本地配置** - 首先尝试加载本地配置
2. **检查API版本** - 同时检查API返回的配置版本
3. **版本比较** - 使用语义化版本比较（支持 v1.0.1 格式）
4. **用户提示** - 仅当API版本更新时才提示用户

### 版本比较规则

```javascript
// 版本比较示例
compareVersions('v1.0.2', 'v1.0.1') // 返回 1 (v1.0.2 > v1.0.1)
compareVersions('v1.0.1', 'v1.0.1') // 返回 0 (相等)
compareVersions('v1.0.1', 'v1.0.2') // 返回 -1 (v1.0.1 < v1.0.2)
```

### 不同场景的处理

- **本地版本 = API版本** → 使用本地配置，无提示
- **本地版本 < API版本** → 提示用户更新，可选择立即更新或稍后更新
- **本地版本 > API版本** → 使用本地配置（开发环境常见）
- **无本地配置** → 首次使用，询问是否下载到本地
- **API请求失败** → 使用本地配置，显示网络错误提示

## 最佳实践

1. **初始化时机** - 在应用启动时调用 `configManager.initialize()`
2. **版本管理** - 定期清理旧版本，保留最新3个版本
3. **错误处理** - 始终提供降级方案，使用本地缓存或默认配置
4. **性能优化** - 使用响应式配置获取器避免重复计算
5. **用户体验** - 提供下载进度提示和错误重试机制
6. **版本策略** - 优先使用本地配置，减少不必要的网络请求

## 注意事项

- 确保HTTP服务在3030端口正常运行
- 配置文件中的URL必须是可访问的
- 大文件下载可能需要较长时间，请提供进度提示
- 不同环境的配置目录是隔离的，不会相互影响
