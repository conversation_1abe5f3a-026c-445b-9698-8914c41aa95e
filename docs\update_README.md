# Electron 更新服务

一个用于管理Electron应用程序更新的Go服务，提供文件监听、版本管理和可视化界面。

## 功能特性

- 🔍 **自动文件监听**: 监听指定目录下的发布文件，自动识别新版本
- 📊 **版本管理**: 支持版本激活、撤销、删除等操作
- 🎯 **版本比对**: 自动解析版本号并进行比较
- 🌐 **Web界面**: 提供直观的可视化管理界面
- 📤 **在线上传**: 支持通过Web界面直接上传发布文件
- ⚡ **electron-updater 支持**: 完全兼容 electron-updater，自动生成更新文件
- 📝 **操作日志**: 记录所有版本管理操作的详细日志
- 🔄 **RESTful API**: 提供完整的API接口供第三方集成

## 快速开始

### 环境要求

- Go 1.21 或更高版本
- 现代浏览器（支持ES6+）

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd electron-update-server
   ```

2. **安装依赖**
   ```bash
   go mod tidy
   ```

3. **运行服务**
   
   **Windows:**
   ```bash
   start.bat
   ```
   
   **Linux/macOS:**
   ```bash
   chmod +x start.sh
   ./start.sh
   ```
   
   **或直接运行:**
   ```bash
   go run main.go
   ```

4. **访问界面**
   
   打开浏览器访问: http://localhost:8080

## 配置说明

服务支持通过配置文件和环境变量进行配置：

### 配置文件 (config.json)

```json
{
  "port": "8080",
  "release_path": "./releases",
  "log_path": "./logs",
  "web_path": "./web"
}
```

### 环境变量

- `PORT`: 服务端口 (默认: 8080)
- `RELEASE_PATH`: 发布文件目录 (默认: ./releases)
- `LOG_PATH`: 日志文件目录 (默认: ./logs)

## 目录结构

```
electron-update-server/
├── main.go                 # 主程序入口
├── config.json            # 配置文件
├── go.mod                 # Go模块文件
├── internal/              # 内部包
│   ├── config/           # 配置管理
│   ├── handlers/         # HTTP处理器
│   ├── logger/           # 日志系统
│   ├── models/           # 数据模型
│   ├── server/           # HTTP服务器
│   └── watcher/          # 文件监听器
├── web/                   # 前端文件
│   ├── index.html        # 主页面
│   ├── styles.css        # 样式文件
│   └── script.js         # JavaScript逻辑
├── releases/              # 发布文件目录 (自动创建)
├── logs/                  # 日志文件目录 (自动创建)
├── start.bat             # Windows启动脚本
├── start.sh              # Linux/macOS启动脚本
└── README.md             # 说明文档
```

## API 接口

### 版本管理

- `GET /api/versions` - 获取所有版本列表
- `GET /api/versions/{version}` - 获取指定版本信息
- `POST /api/versions/{version}/activate` - 激活版本
- `POST /api/versions/{version}/revoke` - 撤销版本
- `DELETE /api/versions/{version}/delete` - 删除版本
- `GET /api/versions/{version}/download` - 下载版本文件
- `POST /api/upload` - 上传发布文件

### 系统信息

- `GET /api/status` - 获取系统状态
- `GET /api/logs` - 获取操作日志

### electron-updater 兼容接口

- `GET /api/update` - electron-updater 更新检查接口
- `GET /latest.yml` - Windows 更新文件
- `GET /latest-mac.yml` - macOS 更新文件
- `GET /latest-linux.yml` - Linux 更新文件

## 使用说明

### 1. 添加发布文件

有两种方式添加发布文件：

**方式一：直接复制文件**
将Electron应用的发布文件（.exe, .dmg, .AppImage等）放入 `releases` 目录，服务会自动检测并添加到版本管理中。

**方式二：Web界面上传**
通过Web界面的"上传文件"按钮，直接上传发布文件到服务器。

支持的文件格式：
- Windows: `.exe`, `.msi`
- macOS: `.dmg`, `.pkg`
- Linux: `.AppImage`, `.deb`, `.rpm`, `.snap`
- 通用: `.zip`, `.tar.gz`, `.tar.bz2`

### 2. 版本号识别

服务支持多种版本号格式：
- `v1.2.3` 或 `1.2.3`
- `1.2.3-beta`
- `*******`

文件名示例：
- `MyApp-v1.2.3.exe`
- `MyApp-1.2.3-beta.dmg`
- `MyApp_1.2.3.AppImage`

### 3. 版本状态

- **待发布 (pending)**: 新检测到的版本，尚未激活
- **已上线 (active)**: 当前激活的版本，用户可以下载
- **已撤销 (revoked)**: 已撤销的版本，不再提供下载
- **已归档 (archived)**: 归档的版本

### 4. Web界面操作

- **版本管理**: 查看所有版本，执行激活、撤销、删除操作
- **操作日志**: 查看所有版本管理操作的历史记录
- **系统设置**: 查看系统状态和配置信息

### 5. electron-updater 集成

激活版本后，系统会自动生成 electron-updater 需要的文件：
- `latest.yml` - Windows 更新配置
- `latest-mac.yml` - macOS 更新配置
- `latest-linux.yml` - Linux 更新配置

在你的 Electron 应用中配置：
```javascript
const { autoUpdater } = require('electron-updater');

autoUpdater.setFeedURL({
  provider: 'generic',
  url: 'http://your-server:8080'
});

autoUpdater.checkForUpdatesAndNotify();
```

详细集成指南请参考: [electron-updater-integration.md](electron-updater-integration.md)

## 开发说明

### 项目架构

- **main.go**: 程序入口，负责初始化和启动各个组件
- **config**: 配置管理模块
- **watcher**: 文件监听模块，监控发布目录变化
- **models**: 数据模型，版本信息管理
- **handlers**: HTTP请求处理器
- **server**: HTTP服务器封装
- **logger**: 日志记录模块

### 扩展开发

1. **添加新的文件格式支持**: 修改 `watcher/watcher.go` 中的 `isReleaseFile` 方法
2. **自定义版本号解析**: 修改 `models/version.go` 中的 `ExtractVersionFromFileName` 方法
3. **添加新的API接口**: 在 `handlers/handlers.go` 中添加新的处理方法
4. **自定义前端界面**: 修改 `web/` 目录下的前端文件

## 故障排除

### 常见问题

1. **端口被占用**
   - 修改 `config.json` 中的端口号
   - 或设置环境变量 `PORT=其他端口号`

2. **文件监听不工作**
   - 检查 `releases` 目录权限
   - 确认文件格式是否支持

3. **版本号识别失败**
   - 检查文件名是否包含版本号
   - 确认版本号格式是否符合规范

4. **Web界面无法访问**
   - 检查防火墙设置
   - 确认服务是否正常启动

### 日志查看

- 应用日志: `logs/app.log`
- API日志: `logs/api.log`

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
