const { app } = require('electron');
const path = require('path');
const os = require('os');

// 模拟不同的应用名称设置
console.log('Testing config paths...');

// 测试test版本
app.setName('popofifi-test');
console.log('Test version app name:', app.getName());
const testUserDataPath = path.join(os.homedir(), 'AppData', 'Roaming', app.getName());
const testConfigPath = path.join(testUserDataPath, 'config.json');
console.log('Test config path:', testConfigPath);

// 测试prod版本
app.setName('popofifi-prod');
console.log('Prod version app name:', app.getName());
const prodUserDataPath = path.join(os.homedir(), 'AppData', 'Roaming', app.getName());
const prodConfigPath = path.join(prodUserDataPath, 'config.json');
console.log('Prod config path:', prodConfigPath);

console.log('Paths are different:', testConfigPath !== prodConfigPath);
