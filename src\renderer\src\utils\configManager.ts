import { ElMessage, ElMessageBox } from 'element-plus'
import electronApi from '@/utils/electronApi'

interface ConfigData {
  config_data: {
    root: Record<string, any>
  }
  config_version: string
}

interface DownloadProgress {
  url: string
  filename: string
  progress: number
  status: 'pending' | 'downloading' | 'completed' | 'error'
  error?: string
}

interface DownloadResult {
  success: boolean
  errors?: string[]
  version?: string
  message?: string
}

interface LocalConfigResult {
  success: boolean
  config?: any
  version?: string
  error?: string
}

interface VersionsResult {
  success: boolean
  versions?: string[]
  currentVersion?: string
  error?: string
}

class ConfigManager {
  private currentConfig: any = null
  private currentVersion: string | null = null
  private baseUrl: string = 'http://localhost:3031'

  /**
   * 下载并缓存配置资源
   */
  async downloadConfigResources(configData: ConfigData): Promise<DownloadResult> {
    try {
      if (!window.api) {
        throw new Error('API not available')
      }

      console.log(`开始下载配置资源，版本: ${configData.config_version}`)
      
      // 显示下载进度提示
      const loadingMessage = ElMessage({
        message: '正在下载配置资源...',
        type: 'info',
        duration: 0,
        showClose: true
      })

      const result = await electronApi.config.downloadConfigResources(configData)
      
      // 关闭加载提示
      loadingMessage.close()

      if (result.success) {
        this.currentVersion = configData.config_version
        ElMessage.success(`配置资源下载完成，版本: ${configData.config_version}`)
        
        // 下载完成后自动加载本地配置
        await this.loadLocalConfig(configData.config_version)
        
        return result
      } else {
        console.error('配置资源下载失败:', result.errors)
        
        // 显示错误详情
        const errorMsg = result.errors?.join('\n') || '未知错误'
        await ElMessageBox.alert(
          `配置资源下载失败:\n${errorMsg}`,
          '下载失败',
          {
            type: 'error',
            confirmButtonText: '重试'
          }
        )
        
        return result
      }
    } catch (error) {
      console.error('下载配置资源时发生错误:', error)
      ElMessage.error(`下载失败: ${error instanceof Error ? error.message : String(error)}`)
      
      return {
        success: false,
        errors: [error instanceof Error ? error.message : String(error)]
      }
    }
  }

  /**
   * 加载本地配置
   */
  async loadLocalConfig(version?: string): Promise<LocalConfigResult> {
    try {
      const result = await electronApi.config.getLocalConfig(version)
      
      if (result.success && result.config) {
        this.currentConfig = result.config
        this.currentVersion = result.version || null
        console.log(`本地配置加载成功，版本: ${this.currentVersion}`)
        return result
      } else {
        console.warn('本地配置不存在或加载失败:', result.error)
        return result
      }
    } catch (error) {
      console.error('加载本地配置时发生错误:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 获取配置值
   */
  getConfigValue(keyPath: string, defaultValue?: any): any {
    if (!this.currentConfig || !this.currentConfig.config_data) {
      console.warn('配置未加载')
      return defaultValue
    }

    const keys = keyPath.split('.')
    let current = this.currentConfig.config_data.root
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key]
      } else {
        console.warn(`配置路径不存在: ${keyPath}`)
        return defaultValue
      }
    }
    
    return current !== undefined ? current : defaultValue
  }

  /**
   * 获取资源URL
   * 如果是本地资源，返回本地HTTP服务URL
   * 如果是远程资源，返回原始URL
   */
  getResourceUrl(keyPath: string): string | null {
    const value = this.getConfigValue(keyPath)
    
    if (!value || typeof value !== 'string') {
      return null
    }

    // 如果是本地资源路径（以/config-resources/开头）
    if (value.startsWith('/config-resources/')) {
      return `${this.baseUrl}${value}`
    }
    
    // 如果是完整的HTTP URL，直接返回
    if (value.startsWith('http://') || value.startsWith('https://')) {
      return value
    }
    
    return null
  }

  /**
   * 获取当前配置版本
   */
  getCurrentVersion(): string | null {
    return this.currentVersion
  }

  /**
   * 获取当前完整配置
   */
  getCurrentConfig(): any {
    return this.currentConfig
  }

  /**
   * 获取可用版本列表
   */
  async getAvailableVersions(): Promise<VersionsResult> {
    try {
      return await electronApi.config.getAvailableVersions()
    } catch (error) {
      console.error('获取可用版本时发生错误:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 切换到指定版本
   */
  async switchToVersion(version: string): Promise<boolean> {
    try {
      // 设置当前版本
      const setResult = await electronApi.config.setCurrentVersion(version)
      if (!setResult.success) {
        throw new Error(setResult.error || '设置版本失败')
      }

      // 加载指定版本的配置
      const loadResult = await this.loadLocalConfig(version)
      if (!loadResult.success) {
        throw new Error(loadResult.error || '加载配置失败')
      }

      // ElMessage.success(`已切换到版本: ${version}`)
      return true
    } catch (error) {
      console.error('切换版本时发生错误:', error)
      ElMessage.error(`切换版本失败: ${error instanceof Error ? error.message : String(error)}`)
      return false
    }
  }

  /**
   * 清理旧版本
   */
  async cleanupOldVersions(keepCount: number = 3): Promise<boolean> {
    try {
      const electronApi = await import('./electronApi')
      const api = electronApi.default

      const result = await api.config.cleanupOldVersions(keepCount)

      if (result.success) {
        ElMessage.success(`已清理旧版本，保留最新 ${keepCount} 个版本`)
        return true
      } else {
        throw new Error(result.error || '清理失败')
      }
    } catch (error) {
      console.error('清理旧版本时发生错误:', error)
      ElMessage.error(`清理失败: ${error instanceof Error ? error.message : String(error)}`)
      return false
    }
  }

  /**
   * 获取下载进度
   */
  async getDownloadProgress(): Promise<DownloadProgress[]> {
    try {
      const result = await electronApi.config.getDownloadProgress()
      return result.success ? result.progress : []
    } catch (error) {
      console.error('获取下载进度时发生错误:', error)
      return []
    }
  }

  /**
   * 检查是否有本地配置
   */
  async hasLocalConfig(version?: string): Promise<boolean> {
    const result = await this.loadLocalConfig(version)
    return result.success && !!result.config
  }

  /**
   * 初始化配置管理器
   * 尝试加载最新的本地配置
   */
  async initialize(): Promise<boolean> {
    try {
      // 获取可用版本
      const versionsResult = await this.getAvailableVersions()

      if (versionsResult.success && versionsResult.currentVersion) {
        // 如果有当前版本，加载它
        const loadResult = await this.loadLocalConfig(versionsResult.currentVersion)
        return loadResult.success
      } else if (versionsResult.success && versionsResult.versions && versionsResult.versions.length > 0) {
        // 如果没有当前版本但有可用版本，加载最新的
        const latestVersion = versionsResult.versions[versionsResult.versions.length - 1]
        const loadResult = await this.loadLocalConfig(latestVersion)
        if (loadResult.success) {
          await this.switchToVersion(latestVersion)
          return true
        }
      }

      console.log('没有找到可用的本地配置')
      return false
    } catch (error) {
      console.error('初始化配置管理器时发生错误:', error)
      return false
    }
  }

  /**
   * 检查配置更新
   * @param apiUrl API地址
   * @returns 更新检查结果
   */
  async checkForUpdates(apiUrl: string = '/api/v1.0/device/config'): Promise<{
    hasUpdate: boolean
    localVersion: string | null
    apiVersion: string | null
    apiData?: any
  }> {
    try {
      const localVersion = this.getCurrentVersion()

      // 获取API配置
      const axios = (await import('axios')).default
      const response = await axios.get(apiUrl)

      if (!response.data || !response.data.data || !response.data.data.config_version) {
        return {
          hasUpdate: false,
          localVersion,
          apiVersion: null
        }
      }

      const apiVersion = response.data.data.config_version

      // 如果没有本地版本，认为需要更新
      if (!localVersion) {
        return {
          hasUpdate: true,
          localVersion,
          apiVersion,
          apiData: response.data.data
        }
      }

      // 比较版本
      const hasUpdate = this.compareVersions(apiVersion, localVersion) > 0

      return {
        hasUpdate,
        localVersion,
        apiVersion,
        apiData: hasUpdate ? response.data.data : undefined
      }
    } catch (error) {
      console.error('检查配置更新时发生错误:', error)
      return {
        hasUpdate: false,
        localVersion: this.getCurrentVersion(),
        apiVersion: null
      }
    }
  }

  /**
   * 版本比较函数
   * @param version1 版本1
   * @param version2 版本2
   * @returns 1: version1 > version2, -1: version1 < version2, 0: 相等
   */
  private compareVersions(version1: string, version2: string): number {
    if (!version1 || !version2) return 0

    // 移除 'v' 前缀并分割版本号
    const v1 = version1.replace(/^v/, '').split('.').map(Number)
    const v2 = version2.replace(/^v/, '').split('.').map(Number)

    // 补齐版本号长度
    const maxLength = Math.max(v1.length, v2.length)
    while (v1.length < maxLength) v1.push(0)
    while (v2.length < maxLength) v2.push(0)

    // 逐位比较
    for (let i = 0; i < maxLength; i++) {
      if (v1[i] > v2[i]) return 1
      if (v1[i] < v2[i]) return -1
    }

    return 0
  }

  /**
   * 手动触发配置更新
   * @param apiUrl API地址
   * @returns 更新结果
   */
  async updateConfig(apiUrl: string = '/api/v1.0/device/config'): Promise<{
    success: boolean
    message: string
    newVersion?: string
  }> {
    try {
      const updateCheck = await this.checkForUpdates(apiUrl)

      if (!updateCheck.hasUpdate) {
        return {
          success: true,
          message: '当前已是最新版本'
        }
      }

      if (!updateCheck.apiData) {
        return {
          success: false,
          message: '无法获取最新配置数据'
        }
      }

      // 下载新配置
      const downloadResult = await this.downloadConfigResources(updateCheck.apiData)

      if (downloadResult.success) {
        return {
          success: true,
          message: `配置已更新到版本 ${updateCheck.apiVersion}`,
          newVersion: updateCheck.apiVersion || undefined
        }
      } else {
        return {
          success: false,
          message: `配置更新失败: ${downloadResult.errors?.join(', ') || '未知错误'}`
        }
      }
    } catch (error) {
      console.error('手动更新配置时发生错误:', error)
      return {
        success: false,
        message: `更新失败: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }
}

// 创建单例实例
const configManager = new ConfigManager()

export default configManager
export { ConfigManager, ConfigData, DownloadProgress, DownloadResult }
