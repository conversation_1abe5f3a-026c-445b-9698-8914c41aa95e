const fs = require('fs');
const path = require('path');
const { app } = require('electron');
const os = require('os')

const defaultConfig = {
    "APP":{
        "style": "",
        "alwaysOnTop": false,
        "fullScreen": false,
        "name": "",
        "pwd": ""
    }
}

let _config = JSON.parse(JSON.stringify(defaultConfig))

const getConfigPath = () => {
    console.log('isPackaged', app.isPackaged)
  if (app.isPackaged) {
    // 打包后，根据构建模式使用不同的userData目录
    // 获取当前应用名称，确保test和prod版本使用不同的配置目录
    const appName = app.getName();
    console.log('App name:', appName);

    // 直接使用应用名称构建配置目录路径，确保完全隔离
    const userDataPath = path.join(os.homedir(), 'AppData', 'Roaming', appName);
    const configPath = path.join(userDataPath, 'config.json');

    // 确保配置目录存在
    if (!fs.existsSync(userDataPath)) {
      fs.mkdirSync(userDataPath, { recursive: true });
    }

    console.log('Config path for packaged app:', configPath);
    return configPath
  } else {
    // 开发环境下，也使用appdata目录，保存到popofifi-dev文件夹下
    const mode = import.meta.env.MODE || 'dev';
    let configFileName = 'config.json';
    if (mode === 'test') {
      configFileName = 'config_test.json';
    } else if (mode === 'prod') {
      configFileName = 'config_prod.json';
    }

    // 开发模式也使用appdata目录，统一保存到popofifi-dev下
    const userDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'popofifi-dev');
    const configPath = path.join(userDataPath, configFileName);

    // 确保配置目录存在
    if (!fs.existsSync(userDataPath)) {
      fs.mkdirSync(userDataPath, { recursive: true });
    }

    console.log('Config path for dev:', configPath)
    return configPath
  }
};

//获取本机ip地址
function getIPAdress() {
    var interfaces = os.networkInterfaces();　　
    let ips = []
    for (var devName in interfaces) {　　　　
        var iface = interfaces[devName];　　　　　　
        for (var i = 0; i < iface.length; i++) {
            var alias = iface[i];
            if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
                ips.push(alias.address);
            }
        }　　
    }
    return ips
}
const saveConfig = () =>{
    // 使用_config而不是config，因为config现在是局部变量
    let __config = JSON.parse(JSON.stringify(_config))
    __config.IPs = undefined
    const configPath = getConfigPath();
    fs.writeFileSync(configPath, JSON.stringify(__config, null, 2));
}
const readConfig = () => {
  const configPath = getConfigPath();
  try {
    const configData = fs.readFileSync(configPath, 'utf-8');
    let json = JSON.parse(configData);
    _config = json
    json.IPs = getIPAdress()
    Object.assign(_config, json)
    return _config
  } catch (error) {
    console.error('Error reading config file');
    return null
  }
};

const setLocalConfig = (key, value) => {
    switch(key){
      case 'name':
        _config.APP.name = value
        break;
      default:
        return
    }
    saveConfig()
}

// 延迟初始化配置，确保app.setName()已经生效
let config: any = null;

const initConfig = () => {
    if (config === null) {
        config = readConfig();
        if (!config) {
            config = _config
            saveConfig()
        }
        console.log('Initialized config:', config);
    }
    return config;
};

// 导出一个getter函数而不是直接导出config
const getConfig = () => {
    return initConfig();
};

export default getConfig
export {setLocalConfig}