# Electron API 使用指南

## 快速开始

### 1. 基本导入

```javascript
import electronApi from '@/utils/electronApi'
```

### 2. 环境检测

```javascript
// 检测当前运行环境
if (electronApi.isElectron()) {
  console.log('运行在 Electron 环境')
} else {
  console.log('运行在浏览器环境')
}
```

## API 分类使用

### 应用控制 (app)

```javascript
// 关闭应用
electronApi.app.closeApp()

// 重启应用
electronApi.app.restartApp()

// 获取应用路径
const appPath = await electronApi.app.getAppPath()
console.log('应用路径:', appPath.path)

// 获取开发环境信息
const devInfo = await electronApi.app.getAppDev()
console.log('环境:', devInfo.env)
```

### 配置管理 (config)

```javascript
// 获取配置文件
const config = await electronApi.config.getConfig()
console.log('应用配置:', config)

// 设置本地配置
await electronApi.config.setLocalConfig('key', 'value')

// 获取本地配置
const localConfig = await electronApi.config.getLocalConfig('1.0.0')
if (localConfig.success) {
  console.log('本地配置:', localConfig.config)
}

// 下载配置资源
const downloadResult = await electronApi.config.downloadConfigResources({
  config_version: '1.0.0',
  resources: [...]
})

// 获取可用版本列表
const versions = await electronApi.config.getAvailableVersions()
console.log('可用版本:', versions.versions)

// 清理旧版本
await electronApi.config.cleanupOldVersions(3)

// 设置当前版本
await electronApi.config.setCurrentVersion('1.0.0')

// 获取配置下载进度
const progress = await electronApi.config.getDownloadProgress()
console.log('配置下载进度:', progress)
```

### 设备信息 (device)

```javascript
// 获取机器信息
const machineInfo = await electronApi.device.getMachineInfo()
console.log('机器ID:', machineInfo.machineId)
```

### 版本管理 (version)

```javascript
// 获取当前版本
const versionResult = await electronApi.version.getVersion()
const version = versionResult.version || versionResult
console.log('当前版本:', version)

// 获取版本信息
const versionInfoResult = await electronApi.version.getVersionInfo()
const versionInfo = versionInfoResult.versionInfo || versionInfoResult
console.log('版本信息:', versionInfo)
```

### 更新功能 (update)

```javascript
// 检查更新
const updateInfo = await electronApi.update.checkForUpdates()
if (updateInfo.hasUpdate) {
  console.log('发现新版本:', updateInfo.latestVersion)
}

// 下载更新
const downloadResult = await electronApi.update.downloadUpdate()
if (downloadResult.success) {
  console.log('更新下载完成')
}

// 安装更新
await electronApi.update.installUpdate()

// 获取下载进度
const progress = await electronApi.update.getDownloadProgress()
console.log('下载进度:', progress)
```

### 开发者工具 (dev)

```javascript
// 切换开发者工具
electronApi.dev.toggleDevTools()
```

### 快捷键 (shortcut)

```javascript
// 执行快捷键操作
await electronApi.shortcut.kuaijiejian({
  name: 'huanlianclose'
})
```

### 全局变量管理 (globals)

```javascript
// 测试模式
const isTest = electronApi.globals.getIsTest()
electronApi.globals.setIsTest(true)

// 客户端ID
const clientId = electronApi.globals.getClientId()
electronApi.globals.setClientId({
  screen_num: 'DEMO_001',
  timestamp: Date.now()
})

// Token
const token = electronApi.globals.getToken()
electronApi.globals.setToken('your-token-here')

// 环境信息
const env = electronApi.globals.getEnv()
electronApi.globals.setEnv('development')
```

## 错误处理

### 基本错误处理

```javascript
try {
  const config = await electronApi.config.getConfig()
  console.log('配置获取成功:', config)
} catch (error) {
  console.error('配置获取失败:', error.message)
}
```

### 统一错误处理

```javascript
const handleApiCall = async (apiCall, errorMessage) => {
  try {
    return await apiCall()
  } catch (error) {
    console.error(`${errorMessage}:`, error.message)
    throw error
  }
}

// 使用示例
const config = await handleApiCall(
  () => electronApi.config.getConfig(),
  '获取配置失败'
)
```

## 在 Vue 组件中使用

### 基本使用

```vue
<template>
  <div>
    <button @click="getConfig">获取配置</button>
    <button @click="closeApp">关闭应用</button>
    <p v-if="config">配置: {{ JSON.stringify(config) }}</p>
  </div>
</template>

<script>
import electronApi from '@/utils/electronApi'

export default {
  data() {
    return {
      config: null
    }
  },
  methods: {
    async getConfig() {
      try {
        this.config = await electronApi.config.getConfig()
      } catch (error) {
        console.error('获取配置失败:', error)
      }
    },
    closeApp() {
      electronApi.app.closeApp()
    }
  }
}
</script>
```

### 在生命周期中使用

```vue
<script>
import electronApi from '@/utils/electronApi'

export default {
  async mounted() {
    // 初始化时获取配置
    try {
      const config = await electronApi.config.getConfig()
      this.initializeWithConfig(config)
    } catch (error) {
      console.error('初始化失败:', error)
    }
  },
  beforeCreate() {
    // 设置全局变量
    const clientId = localStorage.getItem('clientId')
    if (clientId) {
      electronApi.globals.setClientId(JSON.parse(clientId))
    }
  }
}
</script>
```

## 最佳实践

### 1. 环境适配

```javascript
// 根据环境执行不同逻辑
if (electronApi.isElectron()) {
  // Electron 环境特定功能
  await electronApi.dev.toggleDevTools()
} else {
  // 浏览器环境替代方案
  console.log('浏览器环境下无法使用开发者工具')
}
```

### 2. 配置缓存

```javascript
// 缓存配置以避免重复请求
let cachedConfig = null

const getConfig = async () => {
  if (!cachedConfig) {
    cachedConfig = await electronApi.config.getConfig()
  }
  return cachedConfig
}
```

### 3. 全局状态管理

```javascript
// 在 Vuex/Pinia store 中使用
const useConfigStore = defineStore('config', {
  state: () => ({
    config: null,
    isTest: electronApi.globals.getIsTest()
  }),
  actions: {
    async loadConfig() {
      this.config = await electronApi.config.getConfig()
    },
    setTestMode(value) {
      this.isTest = value
      electronApi.globals.setIsTest(value)
    }
  }
})
```

### 4. 错误边界

```javascript
// 创建 API 包装器
const safeApiCall = async (apiCall, fallback = null) => {
  try {
    return await apiCall()
  } catch (error) {
    console.warn('API 调用失败，使用备用方案:', error.message)
    return fallback
  }
}

// 使用示例
const machineInfo = await safeApiCall(
  () => electronApi.device.getMachineInfo(),
  { machineId: 'fallback-id' }
)
```

## 调试技巧

### 1. 日志记录

```javascript
// 启用详细日志
const debugApi = {
  ...electronApi,
  config: {
    ...electronApi.config,
    getConfig: async () => {
      console.log('正在获取配置...')
      const result = await electronApi.config.getConfig()
      console.log('配置获取结果:', result)
      return result
    }
  }
}
```

### 2. 网络监控

```javascript
// 在浏览器环境下监控网络请求
if (!electronApi.isElectron()) {
  const originalFetch = window.fetch
  window.fetch = async (...args) => {
    console.log('API 请求:', args[0])
    const response = await originalFetch(...args)
    console.log('API 响应:', response.status)
    return response
  }
}
```

### 3. 性能监控

```javascript
// 监控 API 调用性能
const measureApiCall = async (name, apiCall) => {
  const start = performance.now()
  try {
    const result = await apiCall()
    const end = performance.now()
    console.log(`${name} 耗时: ${end - start}ms`)
    return result
  } catch (error) {
    const end = performance.now()
    console.error(`${name} 失败 (耗时: ${end - start}ms):`, error)
    throw error
  }
}

// 使用示例
const config = await measureApiCall(
  '获取配置',
  () => electronApi.config.getConfig()
)
```

## 常见问题

### Q: 如何在浏览器环境下测试？

A: 确保 Electron 应用正在运行（主进程 HTTP 服务会自动启动），然后在浏览器中访问 `http://localhost:5173`。

### Q: API 调用失败怎么办？

A: 检查主进程是否正常运行，确认 3030 端口未被占用，查看浏览器控制台和主进程日志。

### Q: 如何添加新的 API？

A: 在 `httpService.ts` 中添加路由，在 `electronApi.js` 中添加方法，并更新文档。

### Q: 全局变量在不同环境下的行为？

A: Electron 环境下直接使用 `window` 对象，浏览器环境下使用 `localStorage` 持久化。
