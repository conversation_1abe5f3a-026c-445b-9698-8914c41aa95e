import {ref,computed} from 'vue'
import { defineStore } from 'pinia'

let styles = localStorage.getItem('styles')
styles = styles ? JSON.parse(styles) : []
const stylesData = ref(styles)

const setStyles = (data) => {
    let dataStr = JSON.stringify(data)
    localStorage.setItem('styles', dataStr)
    stylesData.value = data;
}

const singlePhotoData = ref({
    photo:'',
    photoFile:null,
    gender:''
})

const setSinglePhotoData = (data) => {
    singlePhotoData.value = data;
}

// 全家福拍照相关
export const useSinglePhotoStore = defineStore('singlePhoto',()=>{
    return { singlePhotoData,setSinglePhotoData, stylesData, setStyles }
})