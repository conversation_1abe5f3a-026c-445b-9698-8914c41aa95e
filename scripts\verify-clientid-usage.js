/**
 * 验证 window.clientId.screen_num 使用的脚本
 * 检查所有使用 window.clientId.screen_num 的地方是否都已经更新为 electronApi
 */

const fs = require('fs')
const path = require('path')

// 需要检查的目录
const checkDirs = [
  'src/renderer/src/views',
  'src/renderer/src/components',
  'src/renderer/src/utils',
  'src/renderer/src/store'
]

// 排除的文件
const excludeFiles = [
  'electronApi.js',
  'electronApiTest.js'
]

// 递归获取所有 .vue 和 .js/.ts 文件
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      getAllFiles(filePath, fileList)
    } else if (
      (file.endsWith('.vue') || file.endsWith('.js') || file.endsWith('.ts')) &&
      !excludeFiles.includes(file)
    ) {
      fileList.push(filePath)
    }
  })
  
  return fileList
}

// 检查文件内容
function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8')
  const hasWindowClientId = content.includes('window.clientId.screen_num')
  const hasElectronApiImport = content.includes("import electronApi from '@/utils/electronApi'")
  
  // 查找所有 window.clientId.screen_num 的行号
  const lines = content.split('\n')
  const problemLines = []
  
  lines.forEach((line, index) => {
    if (line.includes('window.clientId.screen_num')) {
      problemLines.push({
        lineNumber: index + 1,
        content: line.trim()
      })
    }
  })
  
  return {
    filePath,
    hasWindowClientId,
    hasElectronApiImport,
    problemLines,
    needsFix: hasWindowClientId
  }
}

// 主函数
function main() {
  console.log('🔍 开始验证 window.clientId.screen_num 使用...\n')
  
  let allFiles = []
  
  // 收集所有文件
  checkDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      getAllFiles(dir, allFiles)
    }
  })
  
  console.log(`📁 总共检查 ${allFiles.length} 个文件\n`)
  
  const results = allFiles.map(checkFile)
  const problemFiles = results.filter(result => result.needsFix)
  
  if (problemFiles.length === 0) {
    console.log('✅ 所有文件都已正确迁移到 electronApi！')
  } else {
    console.log(`❌ 发现 ${problemFiles.length} 个文件仍在使用 window.clientId.screen_num：\n`)
    
    problemFiles.forEach(file => {
      console.log(`📄 ${file.filePath}`)
      console.log(`   导入状态: ${file.hasElectronApiImport ? '✅ 已导入' : '❌ 未导入'} electronApi`)
      console.log('   问题行:')
      file.problemLines.forEach(line => {
        console.log(`     第${line.lineNumber}行: ${line.content}`)
      })
      console.log('')
    })
    
    console.log('🔧 修复建议：')
    console.log('1. 确保每个文件都导入了 electronApi:')
    console.log("   import electronApi from '@/utils/electronApi'")
    console.log('2. 将 window.clientId.screen_num 替换为:')
    console.log('   const clientId = electronApi.globals.getClientId()')
    console.log('   clientId?.screen_num')
    console.log('')
  }
  
  // 统计信息
  const totalProblemLines = problemFiles.reduce((sum, file) => sum + file.problemLines.length, 0)
  
  console.log('📊 统计信息：')
  console.log(`- 检查的文件总数: ${allFiles.length}`)
  console.log(`- 有问题的文件数: ${problemFiles.length}`)
  console.log(`- 问题行总数: ${totalProblemLines}`)
  
  return problemFiles.length === 0
}

// 运行检查
if (require.main === module) {
  const success = main()
  process.exit(success ? 0 : 1)
}

module.exports = { main, checkFile, getAllFiles }
