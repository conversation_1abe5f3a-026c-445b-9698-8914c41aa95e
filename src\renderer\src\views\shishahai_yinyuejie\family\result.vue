<template>
  <div class="purchase">
    <div class="top" id="myTop">
      <div class="logo">
        <img class="img-logo" src="@/assets/yinyuejie/result/logo.png" alt="" @click="isDev" />
      </div>
      <!-- <div class="changeBG" @click="changeBg">切换背景</div> -->
      <img src="@/assets/yinyuejie/result/arrow.png" class="arrow arrowL" @click="changeBg(-1)">
      <img src="@/assets/yinyuejie/result/arrow.png" class="arrow arrowR" @click="changeBg(1)">
      <div class="bgBox" id="bgBox">
        <img class="huodong" src="@/assets/yinyuejie/result/Qbk.png">
        <div class="imgBoxP">
          <div class="imgBox" id="imgBox">
            <img class="bk" :src="bgg" />
            <img
              class="avatar"
              v-for="(item, index) in avatarLst"
              :key="index"
              :style="{ display: item.url ? 'inline' : 'none' }"
              :src="item.url"
              :ref="`board.${index}`"
            />
          </div>
        </div>
      </div>

      <div class="inner-camera">
        <video ref="myVideo" width="100%" height="100%" autoplay playsinline></video>
        <img v-if="capturedImage" class="preview-image" :src="capturedImage" />
        
      </div>
      <div v-if="countdown != 0" class="count-numer">
        {{ countdown }}
      </div>
      <div v-if="shibieLoading" class="loading">
        <base-loading></base-loading>
      </div>
    </div>
    <div class="bottomBox">
      <div class="avatarlst">
        <div
          v-for="(item, index) in otherAvatarLst"
          :key="index"
          class="avatar"
          @click="changeAvatar(index)"
        >
          <img class="user" :src="item.url" v-if="item.url" />
          <img class="border" src="@/assets/yinyuejie/result/border.png">
          <div v-if="!item.url" class="load">
            <base-loading></base-loading>
          </div>
        </div>
      </div>
    </div>
    <activity-bottom
      class="paybox"
      goodCode="V1_FAMILY"
      :isHasRetakeFn="false"
      :themeImg="{
        btnImg:btnBg,
        outerCircleBgImg:btnWraper,
        reTakeBtnTextImg:retakeText,
        backBtnTextImg:backText,
        purchaseTextImg:purchaseText,
        sureBtnTextImg:sureText,
      }"
      @handleCreateOrder="handleReceivedCreateOrder"
      @handleRetake="handleReceivedRetake"
      @handleBack="handleClickBack"
    >
    </activity-bottom>
    <!-- <img class="bottomBk" :src="bkk"> -->
  </div>
</template>

<script>
import bkk from '@/assets/welcome/images/welcome-bottom.png'
// import domToImage from 'dom-to-image'
import { snapdom } from '@zumer/snapdom'
import { uploadImg } from '@/views/popfifi/uploadImg.js'
import { generateUUID } from '@/utils/uuid'
import { base64ToBlob } from '@/utils'
import { ElMessage } from 'element-plus'
import bus from '@/utils/bus'
import ActivityBottom from '@/components/activity/Bottom/index.vue'
import { useFamilyPhotoStore } from '@/store/family'
import axios from 'axios'
import BaseLoading from '@/components/base/Loading/index.vue'
import electronApi from '@/utils/electronApi'
import btnBg from  '@/assets/yinyuejie/bottom/btn-bg.png'
import btnWraper from  '@/assets/yinyuejie/bottom/btn-wraper.png'
import retakeText from  '@/assets/yinyuejie/bottom/retake-text.png'
import backText from  '@/assets/yinyuejie/bottom/back-text.png'
import purchaseText from  '@/assets/yinyuejie/bottom/purchase-text.png'
import sureText from  '@/assets/yinyuejie/bottom/sure-text.png'

import pnd from '@/assets/theme/nd.png'
import pnd1 from '@/assets/theme/nd1.png'
import pnd2 from '@/assets/theme/nd2.png'
import pnd3 from '@/assets/theme/nd3.png'
import pnd4 from '@/assets/theme/nd4.png'
import pnd5 from '@/assets/theme/nd5.png'
import pnd6 from '@/assets/theme/nd6.png'
import pnd7 from '@/assets/theme/nd7.png'


import pvd from '@/assets/theme/vd.png'
import pvd1 from '@/assets/theme/vd1.png'
import pvd2 from '@/assets/theme/vd2.png'
import pvd3 from '@/assets/theme/vd3.png'
import pvd4 from '@/assets/theme/vd4.png'
import pvd5 from '@/assets/theme/vd5.png'
import pvd6 from '@/assets/theme/vd6.png'
import pvd7 from '@/assets/theme/vd7.png'
import pvd8 from '@/assets/theme/vd8.png'
import pvd9 from '@/assets/theme/vd9.png'
import pvd10 from '@/assets/theme/vd10.png'
import pvd11 from '@/assets/theme/vd11.png'
import pvd12 from '@/assets/theme/vd12.png'
import pvd13 from '@/assets/theme/vd13.png'
import pvd14 from '@/assets/theme/vd14.png'
import pvd15 from '@/assets/theme/vd15.png'

import bgg1 from '@/assets/family/bgg1.jpg'
import bgg2 from '@/assets/family/bgg2.jpg'
import bgg3 from '@/assets/family/bgg3.jpg'

let bgLst = [bgg1,bgg2,bgg3]
export default {
  components: { ActivityBottom, BaseLoading },
  data() {
    return {
      bkk,
      shibieLoading: false,
      countdown: 0, // 新增倒计时数据
      capturedImage: null,
      currentGoodItem: {},
      myCanvas: null,
      colorLst: [
        '#FF5733', // 红色
        '#33FF57', // 绿色
        '#3357FF', // 蓝色
        '#F1C40F', // 黄色
        '#8E44AD' // 紫色
      ],
      avatarLst: [],
      otherAvatarLst: [],
      allAvatarLst: [],
      getInterval: null,
      familyPic: '',
      familyPhoto: [],
      timeout: null,
      bgg: bgLst[0],
            btnBg,
      btnWraper,
      retakeText,
      backText,
      purchaseText,
      sureText,
    }
  },
  watch: {
    avatarLst: {
      handler(newVal) {
        let promiseLst = []
        this.$nextTick(async (e) => {
          // 当avatarLst变化时，重新计算每个头像的样式
          newVal.forEach((item, index) => {
            promiseLst.push(
              new Promise((resolve) => {
                let img = this.$refs[`board.${index}`][0]
                if (img.complete) {
                  resolve(img)
                  return
                }
                this.$refs[`board.${index}`][0].onload = async (e) => {
                  let img = this.$refs[`board.${index}`][0]
                  item.img = [img.height, img.width]
                  resolve(img)
                }
              })
            )
          })
          Promise.all(promiseLst)
            .then((imgs) => {
              this.calcImgSize(imgs)
            })
            .catch((error) => {})
        })
      },
      deep: true
    }
  },
  mounted() {
    // this.initCamera()
    this.init()
  },
  unmounted() {
    if (this.timeout) {
      clearTimeout(this.timeout)
    }
  },
  methods: {
    changeBg(value){
     
      let index = bgLst.findIndex(e => e === this.bgg)
      index = (index + value + bgLst.length) % bgLst.length
      this.bgg = bgLst[index]
    },
    isDev() {
      let len = Math.floor(Math.random() * 5 + 1)

      this.avatarLst = []
      this.otherAvatarLst = []
      let manLst = [pnd,pnd1,pnd2,pnd3,pnd4,pnd5,pnd6,pnd7]
      let woLst = [pvd, pvd1, pvd2, pvd3, pvd4, pvd5, pvd6, pvd7, pvd8, pvd9, pvd10, pvd11, pvd12, pvd13, pvd14, pvd15]
      for (let i = 0; i < len; i++) {
        let url = ''
        let isSmall = false
        let random = Math.floor(Math.random() * 4 + 1)
        switch (random) {
          case 1:
            url = manLst[Math.floor(Math.random() * manLst.length)]
            isSmall = false
            break
          case 2:
            url = manLst[Math.floor(Math.random() * manLst.length)]
            isSmall = false
            break
          case 3:
            url = woLst[Math.floor(Math.random() * woLst.length)]
            isSmall = true
            break
          case 4:
            url = woLst[Math.floor(Math.random() * woLst.length)]
            isSmall = true
            break
        }
        this.avatarLst.push({
          url,
          isSmall
        })
        this.otherAvatarLst.push({
          url,
          isSmall
        })
      }
    },
    init() {
      
      
      const state = history.state
      let taskId = state?.taskId || null
      this.familyPic = state?.pic || null
      if (!this.familyPic){
        this.isDev()
        return
      }
      console.log('state', state)
      const useFamilyStore = useFamilyPhotoStore()
      this.familyPhoto = useFamilyStore.familyPhotoData
      this.allAvatarLst = new Array(this.familyPhoto.length * 2).fill({})
      this.avatarLst = new Array(this.familyPhoto.length).fill({})
      this.otherAvatarLst = new Array(this.familyPhoto.length).fill({})
      this.getResult(taskId)
    },
    getResult(taskId) {
      console.log('获取结果', taskId)
      const clientId = electronApi.globals.getClientId()

      axios
        .get('/api/v1.0/digital-avatar/task/2d/get', {
          params: {
            task_id: taskId,
            client_id: clientId?.screen_num
          }
        })
        .then((e) => {
          let check = this.capImg(e.data.data.avatars)
          if (check) {
            return
          } else {
            this.timeout = setTimeout(() => {
              this.getResult(taskId)
            }, 1000)
          }
        })
        .catch((err) => {
          if (err?.code === 4000) {
            this.showErrorToast('获取结果失败：' + (err?.msg || '未知错误'))
            setTimeout(() => {
              this.showError = false
              this.$router.push({ path: '/rebuild2-capture' })
            }, 3000)
          }
        })
    },
    capImg(datas) {
      let check = false
      let len = datas.length
      //e.data.data.avatars[0].styles[0].result_oss
      // let lst = []
      datas.forEach((item, index) => {
        item.styles.forEach((style, sindex) => {
          if (!style.result_url) return
          let data = {
            url: style.result_url,
            isSmall: this.familyPhoto[index]?.ageType != 'big',
            id: style.subtask_id
          }
          if (sindex == 0) {
            // 谁先出现就放到avatarLst里，否则放到otherAvatarLst
            if (!this.avatarLst[index].id) {
              console.log('放到AvatarLst')
              this.avatarLst[index] = data
            } else if (!this.otherAvatarLst[index].id && this.avatarLst[index].id !== style.subtask_id) {
              console.log('放到otherAvatarLst')
              this.otherAvatarLst[index] = data
            }
          } else {
            if (!this.avatarLst[index].id) {
              this.avatarLst[index] = data
              console.log('放到AvatarLst1')
            } else if (!this.otherAvatarLst[index].id && this.avatarLst[index].id !== style.subtask_id) {
              console.log('放到otherAvatarLst1')
              this.otherAvatarLst[index] = data
            }
          }
        })
      })
      // this.allAvatarLst = lst

      if (
        this.avatarLst.filter((e) => e.url).length == len &&
        this.otherAvatarLst.filter((e) => e.url).length == len
      ) {
        check = true
        console.log('获取结果成功')
        console.log(datas)
      }
      return check
      let nowL = this.allAvatarLst.filter((item) => {
        return item.url
      })
      let lstL = lst.filter((item) => {
        return item.url
      })
      if (nowL.length == lstL.length) {
        if (this.avatarLst[this.avatarLst.length - 1].url) {
          return true
        }
      } else {
        this.allAvatarLst = lst
        lst.forEach((item, index) => {
          if (index % 2 == 0) {
            this.avatarLst[index] = item
          } else {
            this.otherAvatarLst[index - this.allAvatarLst.length / 2] = item
          }
        })
      }
      return check
    },
    calcImgSize(imgs) {
      let _dom = document.getElementsByClassName('imgBoxP')[0]
      let overlap = 0.2

      const count = imgs.length
      switch (count) {
        case 4:
          overlap = 0.3
          break
        case 5:
          overlap = 0.35
          break
      }
      // 1. 计算所有头像排布后的总宽度
      let totalWidth = imgs[0].width
      for (let i = 1; i < count; i++) {
        // 取左图40%和右图40%的最小值作为实际遮挡
        const maxLeft = imgs[i - 1].width * overlap
        let maxRight = imgs[i].width * overlap
        maxRight = this.avatarLst[i].isSmall? maxRight*0.8: maxRight
        let actualOverlap = Math.min(maxLeft, maxRight)
        actualOverlap /= 0.68
        totalWidth += imgs[i].width - actualOverlap
      }
      console.log(_dom.offsetWidth)
      // 2. 容器宽度（imgBoxP 设为 84vw，这里用 84vw 计算）
      const containerWidth = (90 * _dom.offsetWidth) / 100 // px
      // 3. 居中偏移（px）
      const offset = (containerWidth - totalWidth) / 2 
      // 4. 依次设置每个头像的 left
      let leftPx = offset
      for (let i = 0; i < count; i++) {
        let dom = this.$refs[`board.${i}`][0]
        dom.style.transform = this.avatarLst[i].isSmall ? 'scale(0.8)' : 'scale(1)'
        const leftVw = (leftPx / _dom.offsetWidth) * 100
        dom.style.left = `${leftVw}vw`
        const center = (count - 1) / 2
        const zIndex = 100 - Math.abs(i - center) + (this.avatarLst[i].isSmall ? 101 : 0)
        dom.style.zIndex = Math.floor(zIndex)
        let isFlip = i < Math.floor(count / 2)
        // isFlip = !isFlip
        if (isFlip) {
          dom.style.transform += ' rotateY(180deg)'
        }
        if (i < count - 1) {
          const maxLeft = imgs[i].width * overlap
          let maxRight = imgs[i + 1].width * overlap
          maxRight = this.avatarLst[i].isSmall? maxRight*0.8: maxRight
          let actualOverlap = Math.min(maxLeft, maxRight)
          actualOverlap /= 0.68
          console.log(
            `宽度：${imgs[i].width},下一个宽度：${imgs[i + 1].width},实际遮挡：${actualOverlap}`
          )
          leftPx += imgs[i].width - actualOverlap
        }
      }
    },
    changeAvatar(avatarIndex) {
      // 图上为空则不替换
      if (!this.avatarLst[avatarIndex].url)return
      // 这里可以根据avatarIndex来切换不同的头像
      console.log('切换头像到索引:', avatarIndex)
      let item = this.otherAvatarLst[avatarIndex]
      if (!item.url) return
      this.otherAvatarLst[avatarIndex] = this.avatarLst[avatarIndex]
      this.avatarLst[avatarIndex] = item
      // 例如，可以设置一个当前选中的头像索引
      // this.currentAvatarIndex = avatarIndex;
    },

    handleDomToImageCopy() {
      this.shibieLoading = true
      let that = this
      const targetElement = document.getElementById('imgBox')
      // 获取原始尺寸
      const originalWidth = targetElement.offsetWidth
      const originalHeight = targetElement.offsetHeight
      // 设置最大尺寸
      const maxSize = 1400
      let scale = 1
      // 计算缩放比例
      if (originalWidth > maxSize || originalHeight > maxSize) {
        scale = Math.min(maxSize / originalWidth, maxSize / originalHeight)
      }
      console.time('domToImage')
      this.$nextTick(async () => {
        let img = await snapdom.toJpg(targetElement, { dpr: scale })
        if (img) {
          const blob = base64ToBlob(img.src)
          const file = new File([blob], `${generateUUID()}.jpg`, { type: 'image/jpg' })
          let pic = await uploadImg(file, that.familyPic.avatarId)
          that.shibieLoading = false
          that.handleComputedOrderData(pic)
        }
      })
      // domToImage
      //   .toJpeg(targetElement, {
      //     width: originalWidth * scale,
      //     height: originalHeight * scale,
      //     style: {

      //       transform: `scale(${scale})`,
      //       transformOrigin: 'top left',
      //       overflow: 'visible'
      //     },
      //     quality: 0.95
      //   })
      //   .then(async (base64) => {
      //     // 使用封装好的转换函数
      //     const blob = base64ToBlob(base64)
      //     console.timeEnd('domToImage')
      //     const file = new File([blob], `${generateUUID()}.jpg`, { type: 'image/jpg' })
      //     let pic = await uploadImg(file, this.familyPic.avatarId)
      //     this.shibieLoading = false
      //     this.handleComputedOrderData(pic)
      //     // 自动下载 file 文件
      //     // const url = URL.createObjectURL(file)
      //     // const a = document.createElement('a')
      //     // a.href = url
      //     // a.download = file.name
      //     // document.body.appendChild(a)
      //     // a.click()
      //     // document.body.removeChild(a)
      //     // URL.revokeObjectURL(url)
      //     // let upRes = await uploadImg(file)
      //     // console.log(upRes, 'dsdsdsd')
      //     // upRes.objectKey && that.handleComputedOrderData(upRes)
      //   })
      //   .catch(function (error) {
      //     ElMessage({
      //       message: '发生错误:domToImage',
      //       type: 'error'
      //    })
      //     console.error('oops, something went wrong!', error)
      //   })
    },
    // 组装创建订单的数据给组件
    handleComputedOrderData(upRes) {
      const { id, type, price } = this.currentGoodItem
      const goodData = [
        {
          goods_id: id,
          goods_num: 1,
          goods_type: type,
          style: type,
          pic_url: upRes.objectKey,
          discount_id: null
        }
      ]
      let pramsObj = {
        business_data: goodData,
        payment_amount: price,
        original_amount: price,
        discount_id: null,
        origi_pic_url: this.familyPic.objectKey,
        digital_avatar_id: this.familyPic.avatarId
      }
      bus.emit('orderData', pramsObj)
    },
    handleReceivedCreateOrder(currentGoodItem) {
      console.log('接收到创建订单的事件', currentGoodItem)
      this.currentGoodItem = currentGoodItem
      this.handleDomToImageCopy()
    },
    handleReceivedRetake() {
      // this.capturedImage = null;
      // this.$refs.myVideo.play();
      this.$router.push({ path: '/family-home' })
    },
    handleClickBack() {
      this.$router.push({ path: '/welcome' })
    },
    handleCountDecrease(count) {
      this.countdown = count
    },
    handleCountFinished() {
      this.capturePhoto()
    }
  }
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: 'DouyinSansBold';
  src: url('@/assets/fonts/DouyinSansBold.ttf') format('truetype');
}
.arrowL, .arrowR{
  position: absolute;
  z-index: 10;
  width: 200rem;
  height: 200rem;
  top: 35vh;
  
}
.arrowL{
  left: 0;
}
.arrowR{
  right: 0;
  transform: rotate(180deg);
}
.loading {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  // 新增毛玻璃效果
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px); // 兼容Safari
}

.bottomBk, .paybox{
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 20vh;
  z-index: 10;
  object-fit: cover;
}
.bottomBox {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 35vh;
  display: flex;
  justify-content: center;
  // background: rgba(0, 0, 0, 0.3);
  border-radius: 7vh 7vh 0 0;
  z-index: 10;
  .avatarlst {
    height: 33%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 90%;
    // overflow-x: auto; /* 添加水平滚动条 */
    
    .avatar {
      box-sizing: border-box;
      width: 10vh;
      height: 10vh;
      border-radius: 50%;
      padding: 2vw;
      object-fit: contain; /* 保持图片比例 */
      height: 80%;
      position:relative;
      .border{
        position: absolute;
        top: 0;
        left: 0;
      }
      .user {
        position: absolute;
        top: -30%;
        height: 110%;
        left: 0;
        z-index: 1;
      }
      img {
        width: 80%;
        height: 100%;
        object-fit: contain; /* 保持图片比例 */
      }
      .load {
        width: 80%;
        height: 100%;
        /* display: flex
    ; */
        /* justify-content: center; */
        /* align-items: center; */
        transform: scale(0.8);
        position: absolute;
        top: 0;
        left: 0;
      }
    }
  }
}
.changeBG{
  position: absolute;
  top: 11vh;
  width: 250rem;
  height: 100rem;
  border-bottom-left-radius: 50rem;
  border-top-left-radius: 50rem;
  font-size: 50rem;
  line-height: 110rem;
  right: 0;
  z-index: 1;
  color: #fff;
  background:linear-gradient(250deg, rgba(170, 242, 170, 1) 0%, rgba(247, 129, 206, 1) 100%);
}
.bgBox {
  position: absolute;
  left: 0;
  top: 0vh;
  width: 100vw;
  height: 75vw;
  z-index: 2;
  pointer-events: none;
  .blur {
    width: 100%;
    height: 100%;
    filter: blur(10px) saturate(0.7) brightness(1.1);
    // height: 100%;
    object-fit: cover;
  }
  .cover {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    transform: rotateZ(10deg);
    border-radius: 100rem;
    background: rgba(255, 255, 255, 0.8);
  }
  .huodong{
    position: absolute;
    left: 0;
    z-index:-1;
    width: 100%;
    pointer-events: none;
  }
  .imgBoxP {
    position: absolute;
    top: 40vw;
    left: 9.2vw;
    width: 80.2vw;
    height: calc(100% - 18.0vw);
    z-index: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    .imgBox {
      height: 100%;
      width: 100%;
      .avatar {
        transform-origin: center bottom;
        position: absolute;
        bottom: 4%;
        transition: all 0.3s;
        height: 68%;
        overflow: visible;
      }
    }
    .bk {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
.purchase {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: 'DouyinSansBold', sans-serif;
  .top {
    height: 80%;
    width: 100%;
    position: relative;
    .logo {
      position: absolute;
      left: 0;
      top: -40rem;
      width: 100%;
      .img-logo {
        width: 80vw;
        position: absolute;
        z-index: 555;
        margin-top: 100rem;
        left: 10vw;
      }
    }
    .my-img {
      width: 100%; // 修改为100%填充
      height: 100%; // 修改为100%填充
    }
    .inner-camera {
      position: absolute;
      width: 130%;
      height: 130%;
      left: 0;
      top: 0;
      z-index: -10;
      video {
        // transform: scaleX(-1); /* 镜像翻转 */
        object-fit: cover; /* 填充容器 */
        background: #000; /* 黑底占位 */
      }
      .preview-image {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
      }
    }
    .count-numer {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 507rem;
      color: #fff;
      font-weight: 700;
    }
  }
}
</style>
