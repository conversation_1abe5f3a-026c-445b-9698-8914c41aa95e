# Electron API 统一入口文档

## 概述

本项目实现了 Electron API 的统一入口管理，通过主进程的 HTTP 服务中继 API 功能，支持在 Electron 和浏览器环境下进行开发调试。

## 快速开始

### 1. 基本使用

```javascript
import electronApi from '@/utils/electronApi'

// 获取配置
const config = await electronApi.config.getConfig()

// 关闭应用
electronApi.app.closeApp()

// 获取机器信息
const machineInfo = await electronApi.device.getMachineInfo()
```

### 2. 开发模式

#### Electron 开发（推荐）
```bash
npm run dev
```

#### 浏览器调试
1. 启动 Electron 应用（主进程 HTTP 服务自动启动在 3030 端口）
2. 在浏览器中访问 `http://localhost:5173`

## 文档目录

- [集成指南](./electron-api-integration.md) - 详细的架构设计和集成说明
- [使用指南](./electron-api-usage.md) - 完整的 API 使用文档和最佳实践

## 示例文件

- [完整示例](../src/renderer/src/examples/electronApiExample.vue) - 包含所有 API 使用示例的 Vue 组件

## API 分类

### 应用控制 (app)
- `closeApp()` - 关闭应用
- `restartApp()` - 重启应用
- `getAppPath()` - 获取应用路径
- `getAppDev()` - 获取开发环境信息

### 配置管理 (config)
- `getConfig()` - 获取配置文件
- `setLocalConfig()` - 设置本地配置
- `getLocalConfig()` - 获取本地配置
- `downloadConfigResources()` - 下载配置资源
- `getAvailableVersions()` - 获取可用版本列表
- `cleanupOldVersions()` - 清理旧版本
- `setCurrentVersion()` - 设置当前版本

### 设备信息 (device)
- `getMachineInfo()` - 获取机器信息

### 版本管理 (version)
- `getVersion()` - 获取当前版本
- `getVersionInfo()` - 获取版本信息

### 更新功能 (update)
- `checkForUpdates()` - 检查更新
- `downloadUpdate()` - 下载更新
- `installUpdate()` - 安装更新
- `getDownloadProgress()` - 获取下载进度

### 开发者工具 (dev)
- `toggleDevTools()` - 切换开发者工具

### 快捷键 (shortcut)
- `kuaijiejian()` - 执行快捷键操作

### 全局变量管理 (globals)
- `getIsTest()` / `setIsTest()` - 测试模式
- `getClientId()` / `setClientId()` - 客户端ID
- `getToken()` / `setToken()` - Token
- `getEnv()` / `setEnv()` - 环境信息

## 环境检测

```javascript
if (electronApi.isElectron()) {
  // Electron 环境
} else {
  // 浏览器环境
}
```

## 错误处理

```javascript
try {
  const config = await electronApi.config.getConfig()
} catch (error) {
  console.error('API 调用失败:', error.message)
}
```

## 主要特性

- ✅ **环境自动检测** - 自动适配 Electron 和浏览器环境
- ✅ **统一接口** - 所有 API 调用通过一个入口
- ✅ **HTTP 中继** - 主进程 3030 服务中继 API 功能
- ✅ **开发友好** - 支持浏览器环境开发调试
- ✅ **向后兼容** - 保留原始 API 访问方式
- ✅ **错误处理** - 完善的错误处理机制
- ✅ **类型安全** - 提供清晰的 API 接口

## 注意事项

1. **服务依赖** - 浏览器环境需要主进程 HTTP 服务运行
2. **端口占用** - 确保 3030 端口未被占用
3. **异步调用** - 所有 API 调用都是异步的
4. **错误处理** - 建议为所有 API 调用添加错误处理

## 故障排除

### 常见问题

1. **API 调用失败**
   - 检查主进程是否正常运行
   - 确认 3030 端口未被占用
   - 查看浏览器控制台和主进程日志

2. **环境检测错误**
   - 确保正确导入 `electronApi`
   - 检查 `window.electron` 和 `window.api` 是否正确暴露

3. **类型错误**
   - 确保使用正确的 API 方法名
   - 检查参数格式是否正确

### 调试技巧

1. 使用浏览器开发者工具查看网络请求
2. 检查主进程控制台日志
3. 使用 `electronApi.isElectron()` 确认当前环境

## 扩展指南

如需添加新的 API：

1. 在 `src/main/services/httpService.ts` 中添加对应的路由
2. 在 `src/renderer/src/utils/electronApi.js` 中添加对应的方法
3. 更新文档和示例

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

本项目采用 MIT 许可证。
