/**
 * Electron API 统一入口文件
 * 提供统一的 API 接口，支持 Electron 环境和浏览器开发环境
 * 浏览器环境下通过主进程的 3031 服务中继 API 功能
 */

// 检测当前环境
const isElectron = () => {
  return typeof window !== 'undefined' && window.electron && window.api
}

// 主进程服务基础 URL
const MAIN_SERVER_URL = 'http://localhost:3031'

// 通用的 HTTP 请求函数
const apiRequest = async (endpoint, data = null) => {
  try {
    const options = {
      method: data ? 'POST' : 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
    
    if (data) {
      options.body = JSON.stringify(data)
    }
    
    const response = await fetch(`${MAIN_SERVER_URL}${endpoint}`, options)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    return await response.json()
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error)
    throw error
  }
}

// 应用控制相关 API
export const appApi = {
  // 关闭应用
  closeApp: () => {
    if (isElectron()) {
      return window.electron.ipcRenderer.send('close-app')
    } else {
      return apiRequest('/api/app/close')
    }
  },
  
  // 重启应用
  restartApp: () => {
    if (isElectron()) {
      return window.api.restartApp()
    } else {
      return apiRequest('/api/app/restart')
    }
  },
  
  // 获取应用路径
  getAppPath: () => {
    if (isElectron()) {
      return window.api.getAppPath()
    } else {
      return apiRequest('/api/app/path')
    }
  },
  
  // 获取开发环境信息
  getAppDev: () => {
    if (isElectron()) {
      return window.api.getAppDev()
    } else {
      return apiRequest('/api/app/dev')
    }
  }
}

// 配置相关 API
export const configApi = {
  // 获取配置文件
  getConfig: () => {
    if (isElectron()) {
      return window.api.getConfig()
    } else {
      return apiRequest('/api/config/get')
    }
  },
  
  // 设置本地配置
  setLocalConfig: (...params) => {
    if (isElectron()) {
      return window.api.setlocalConfig(...params)
    } else {
      return apiRequest('/api/config/set', { params })
    }
  },
  
  // 获取本地配置
  getLocalConfig: (version) => {
    if (isElectron()) {
      return window.api.getLocalConfig(version)
    } else {
      const url = version ? `/api/config/local?version=${encodeURIComponent(version)}` : '/api/config/local'
      return fetch(`http://localhost:3031${url}`)
        .then(response => response.json())
        .catch(error => ({ success: false, error: error.message }))
    }
  },
  
  // 下载配置资源
  downloadConfigResources: (configData) => {
    if (isElectron()) {
      return window.api.downloadConfigResources(configData)
    } else {
      return apiRequest('/api/config/download', { configData })
    }
  },
  
  // 获取可用版本列表
  getAvailableVersions: () => {
    if (isElectron()) {
      return window.api.getAvailableVersions()
    } else {
      return apiRequest('/api/config/versions')
    }
  },
  
  // 清理旧版本
  cleanupOldVersions: (keepCount) => {
    if (isElectron()) {
      return window.api.cleanupOldVersions(keepCount)
    } else {
      return apiRequest('/api/config/cleanup', { keepCount })
    }
  },
  
  // 设置当前版本
  setCurrentVersion: (version) => {
    if (isElectron()) {
      return window.api.setCurrentVersion(version)
    } else {
      return apiRequest('/api/config/version/set', { version })
    }
  },

  // 获取配置下载进度
  getDownloadProgress: () => {
    if (isElectron()) {
      return window.api.getDownloadProgress()
    } else {
      return apiRequest('/api/config/progress')
    }
  }
}

// 设备信息相关 API
export const deviceApi = {
  // 获取机器信息
  getMachineInfo: () => {
    if (isElectron()) {
      return window.api.getMachineInfo()
    } else {
      return apiRequest('/api/device/machine-info')
    }
  }
}

// 版本相关 API
export const versionApi = {
  // 获取版本号
  getVersion: () => {
    if (isElectron()) {
      return window.api.getVersion()
    } else {
      return apiRequest('/api/version/current')
    }
  },
  
  // 获取版本信息
  getVersionInfo: () => {
    if (isElectron()) {
      return window.api.getVersionInfo()
    } else {
      return apiRequest('/api/version/info')
    }
  }
}

// 更新相关 API
export const updateApi = {
  // 检查更新
  checkForUpdates: () => {
    if (isElectron()) {
      return window.api.checkForUpdates()
    } else {
      return apiRequest('/api/update/check')
    }
  },
  
  // 下载更新
  downloadUpdate: () => {
    if (isElectron()) {
      return window.api.downloadUpdate()
    } else {
      return apiRequest('/api/update/download')
    }
  },
  
  // 安装更新
  installUpdate: () => {
    if (isElectron()) {
      return window.api.installUpdate()
    } else {
      return apiRequest('/api/update/install')
    }
  },
  
  // 获取更新下载进度
  getUpdateProgress: () => {
    if (isElectron()) {
      return window.api.getDownloadProgress()
    } else {
      return apiRequest('/api/update/progress')
    }
  }
}

// 开发者工具相关 API
export const devApi = {
  // 切换开发者工具
  toggleDevTools: () => {
    if (isElectron()) {
      return window.api.toggleDevTools()
    } else {
      return apiRequest('/api/dev/toggle-devtools')
    }
  }
}

// 快捷键相关 API
export const shortcutApi = {
  // 快捷键操作
  kuaijiejian: (params) => {
    if (isElectron()) {
      return window.api.kuaijiejian(params)
    } else {
      return apiRequest('/api/shortcut/execute', { params })
    }
  }
}

// 全局变量管理
export const globalVars = {
  // 获取测试模式状态
  getIsTest: () => {
    if (isElectron()) {
      return window.isTest
    } else {
      return window.isTest
      const stored = localStorage.getItem('isTest')
      return stored ? JSON.parse(stored) : true
    }
  },

  // 设置测试模式状态
  setIsTest: (value) => {
    console.log('ssssssssssssssssssss')
    if (isElectron()) {
      window.isTest = value
    } else {
      window.isTest = value
      // localStorage.setItem('isTest', JSON.stringify(value))
    }
  },

  // 获取客户端ID
  getClientId: () => {
    if (isElectron()) {
      return window.clientId
    } else {
      const stored = localStorage.getItem('clientId')
      return stored ? JSON.parse(stored) : null
    }
  },

  // 设置客户端ID
  setClientId: (clientId) => {
    if (isElectron()) {
      window.clientId = clientId
    } else {
      localStorage.setItem('clientId', JSON.stringify(clientId))
    }
  },

  // 获取 token
  getToken: () => {
    if (isElectron()) {
      return window.token
    } else {
      return localStorage.getItem('token')
    }
  },

  // 设置 token
  setToken: (token) => {
    if (isElectron()) {
      window.token = token
    } else {
      localStorage.setItem('token', token)
    }
  },

  // 获取环境信息
  getEnv: () => {
    if (isElectron()) {
      return window.env
    } else {
      return 'development'
    }
  },

  // 设置环境信息
  setEnv: (env) => {
    if (isElectron()) {
      window.env = env
    } else {
      console.warn('Cannot set env in browser environment')
    }
  }
}

// 统一的 API 对象，包含所有分类的 API
const electronApi = {
  app: appApi,
  config: configApi,
  device: deviceApi,
  version: versionApi,
  update: updateApi,
  dev: devApi,
  shortcut: shortcutApi,
  globals: globalVars,

  // 环境检测
  isElectron: isElectron,

  // 直接访问原始 API（向后兼容）
  raw: {
    electron: isElectron() ? window.electron : null,
    api: isElectron() ? window.api : null
  }
}
window.electronApi = electronApi
// 默认导出
export default electronApi
