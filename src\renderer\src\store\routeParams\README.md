# 路由传值Store

## 概述

这个store用于解决大型数据的路由跳转传值问题，替代原来使用`history.state`的方式。

## 功能

- 存储和管理路由传值数据
- 支持设置和获取完整的路由参数对象
- 支持设置和获取单个路由参数
- 提供清空路由参数的功能

## API

### 方法

- `setRouteParams(data)` - 设置完整的路由传值数据
- `getRouteParams()` - 获取完整的路由传值数据
- `clearRouteParams()` - 清空所有路由传值数据
- `setRouteParam(key, value)` - 设置单个路由参数
- `getRouteParam(key)` - 获取单个路由参数

### 使用示例

```javascript
import { useRouteParamsStore } from '@/store/routeParams'

// 在组件中使用
const useRouteParams = useRouteParamsStore()

// 设置路由参数
const routeData = {
  subjectFile: file,
  codes: ['001', '002'],
  code: '001',
  gender: 'female'
}
useRouteParams.setRouteParams(routeData)

// 获取路由参数
const data = useRouteParams.getRouteParams()

// 清空路由参数
useRouteParams.clearRouteParams()
```

## 修改的页面

### 发送数据的页面
- `src/renderer/src/views/popfifi/welcome.vue`
- `src/renderer/src/views/shishahai_yinyuejie/welcome.vue`
- `src/renderer/src/activityPage/xingyunshike.vue`

### 接收数据的页面
- `src/renderer/src/views/popfifi/loading.vue`
- `src/renderer/src/views/shishahai_yinyuejie/loading.vue`
- `src/renderer/src/views/popfifi/result.vue`
- `src/renderer/src/views/shishahai_yinyuejie/result.vue`

## 优势

1. **避免URL长度限制** - 不再通过`history.state`传递大型数据
2. **更好的数据管理** - 集中管理路由传值数据
3. **类型安全** - 可以更好地控制数据类型
4. **调试友好** - 所有路由传值都有日志记录
5. **内存管理** - 可以主动清理不需要的数据

## 注意事项

1. 数据存储在内存中，页面刷新后会丢失
2. 建议在不需要数据时主动调用`clearRouteParams()`清理内存
3. 大型文件对象仍需要谨慎处理，避免内存泄漏
