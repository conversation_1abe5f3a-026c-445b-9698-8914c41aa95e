import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import vue from '@vitejs/plugin-vue'
import mkcert from "vite-plugin-mkcert"

// 获取构建模式
const getBuildMode = () => {
  const args = process.argv;
  const modeIndex = args.indexOf('--mode');
  if (modeIndex !== -1 && modeIndex + 1 < args.length) {
    return args[modeIndex + 1];
  }
  return process.env.NODE_ENV === 'development' ? 'dev' : 'prod';
};

const buildMode = getBuildMode();
console.log('Build mode in config:', buildMode);

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()],
    define: {
      'import.meta.env.MODE': JSON.stringify(buildMode)
    }
  },
  preload: {
    plugins: [externalizeDepsPlugin()]
  },
  renderer: {
    define: {
      '__APP_ENV__': JSON.stringify({
        VERSION:process.env.npm_package_version,
        ELECTRON_ENV: process.env.ELECTRON_ENV
      }),
    },
    envDir: resolve(process.cwd()), // 新增环境文件目录配置
    mode: process.env.NODE_ENV || 'production',
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src'),
        '@': resolve('src/renderer/src')
      }
    },


    plugins: [vue(),mkcert()],
    server: {
      host: '0.0.0.0', // 允许外部访问
      port: 3000,       // 指定端口（可选）
      strictPort: true, // 严格模式（端口被占用则报错）

    }
  },

})
