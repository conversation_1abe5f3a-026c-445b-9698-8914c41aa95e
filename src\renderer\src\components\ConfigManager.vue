<template>
  <div class="config-manager">
    <el-card header="配置资源管理">
      <div class="section">
        <h3>当前状态</h3>
        <p><strong>当前版本:</strong> {{ currentVersion || '未设置' }}</p>
        <p><strong>配置状态:</strong> {{ configLoaded ? '已加载' : '未加载' }}</p>
      </div>

      <div class="section">
        <h3>下载配置资源</h3>
        <el-button 
          type="primary" 
          @click="downloadSampleConfig"
          :loading="downloading"
        >
          下载示例配置
        </el-button>
        <el-button 
          type="success" 
          @click="downloadFromApi"
          :loading="downloading"
        >
          从API下载配置
        </el-button>
      </div>

      <div class="section" v-if="downloadProgress.length > 0">
        <h3>下载进度</h3>
        <div v-for="progress in downloadProgress" :key="progress.url" class="progress-item">
          <div class="progress-header">
            <span>{{ progress.filename }}</span>
            <span :class="getStatusClass(progress.status)">{{ getStatusText(progress.status) }}</span>
          </div>
          <el-progress 
            :percentage="progress.progress" 
            :status="progress.status === 'error' ? 'exception' : progress.status === 'completed' ? 'success' : undefined"
          />
          <div v-if="progress.error" class="error-text">{{ progress.error }}</div>
        </div>
      </div>

      <div class="section">
        <h3>版本管理</h3>
        <div class="version-controls">
          <el-select v-model="selectedVersion" placeholder="选择版本" style="width: 200px;">
            <el-option
              v-for="version in availableVersions"
              :key="version"
              :label="version"
              :value="version"
            />
          </el-select>
          <el-button @click="switchVersion" :disabled="!selectedVersion">切换版本</el-button>
          <el-button @click="refreshVersions">刷新</el-button>
          <el-button type="warning" @click="cleanupOldVersions">清理旧版本</el-button>
        </div>
      </div>

      <div class="section" v-if="configLoaded">
        <h3>配置预览</h3>
        <div class="config-preview">
          <div class="config-item">
            <strong>背景图片:</strong>
            <div v-if="backgroundUrl">
              <img :src="backgroundUrl" alt="背景图片" class="preview-image" />
              <p>{{ backgroundUrl }}</p>
            </div>
            <div v-else>未配置</div>
          </div>
          
          <div class="config-item">
            <strong>Logo:</strong>
            <div v-if="logoUrl">
              <img :src="logoUrl" alt="Logo" class="preview-image" />
              <p>{{ logoUrl }}</p>
            </div>
            <div v-else>未配置</div>
          </div>

          <div class="config-item">
            <strong>相机按钮组:</strong>
            <div v-if="cameraButtons" class="button-group">
              <div v-for="(url, key) in cameraButtons" :key="key" class="button-item">
                <span>{{ key }}:</span>
                <img :src="getResourceUrl(`common.cameraBtnGroup.${key}`)" :alt="key" class="preview-button" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="section">
        <h3>配置测试</h3>
        <el-input 
          v-model="testKeyPath" 
          placeholder="输入配置路径，如: common.background"
          style="width: 300px; margin-right: 10px;"
        />
        <el-button @click="testConfigValue">测试获取</el-button>
        <div v-if="testResult" class="test-result">
          <strong>结果:</strong> {{ testResult }}
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import configManager from '../utils/configManager'
import type { DownloadProgress } from '../utils/configManager'

// 响应式数据
const currentVersion = ref<string | null>(null)
const configLoaded = ref(false)
const downloading = ref(false)
const downloadProgress = ref<DownloadProgress[]>([])
const availableVersions = ref<string[]>([])
const selectedVersion = ref<string>('')
const testKeyPath = ref('common.background')
const testResult = ref<string>('')

// 计算属性
const backgroundUrl = computed(() => {
  return configManager.getResourceUrl('common.background')
})

const logoUrl = computed(() => {
  return configManager.getResourceUrl('common.logo')
})

const cameraButtons = computed(() => {
  return configManager.getConfigValue('common.cameraBtnGroup')
})

// 方法
const getResourceUrl = (keyPath: string) => {
  return configManager.getResourceUrl(keyPath)
}

const downloadSampleConfig = async () => {
  downloading.value = true
  
  // 使用示例数据
  const sampleConfig = {
    config_data: {
      root: {
        common: {
          background: "https://qcard-test.oss-cn-beijing.aliyuncs.com/dev%2F00%2F00%2F0000000000000000000000000000%2Fthing%2Fcbc01a0a-96a3-44d5-b36d-60a7df995274.png?Expires=8060452555&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=DhAbWFG1kOJXeOjSogS6xUF%2F4oo%3D",
          logo: "https://qcard-test.oss-cn-beijing.aliyuncs.com/dev%2F00%2F00%2F0000000000000000000000000000%2Fthing%2F3a324c2c-e7bc-494f-8e99-336d420ff9a1.png?Expires=8060452716&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=SHmtp4%2BWouTTBX19nsYYIYP22a4%3D",
          cameraBtnGroup: {
            back: "https://qcard-test.oss-cn-beijing.aliyuncs.com/dev%2F00%2F00%2F0000000000000000000000000000%2Fthing%2Fe1463192-ea72-4ea8-809a-3c99d2133dba.png?Expires=8060452756&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=cO38qjBXUQ1tHSmubsD2x6N63%2BY%3D",
            buy: "https://qcard-test.oss-cn-beijing.aliyuncs.com/dev%2F00%2F00%2F0000000000000000000000000000%2Fthing%2F0fc5467f-cb03-41c0-b0e0-cf1ba787f2a6.png?Expires=8060452860&OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Signature=dhZSvxyqlBq0xcQtGF5Kb95X1E8%3D"
          }
        }
      }
    },
    config_version: `v1.0.${Date.now()}`
  }

  try {
    await configManager.downloadConfigResources(sampleConfig)
    await updateStatus()
    await startProgressMonitoring()
  } finally {
    downloading.value = false
  }
}

const downloadFromApi = async () => {
  downloading.value = true
  
  try {
    // 这里应该从实际API获取配置
    // const response = await axios.get('/api/v1.0/device/config')
    // await configManager.downloadConfigResources(response.data)
    
    ElMessage.warning('请实现从API获取配置的逻辑')
  } finally {
    downloading.value = false
  }
}

const startProgressMonitoring = async () => {
  const checkProgress = async () => {
    const progress = await configManager.getDownloadProgress()
    downloadProgress.value = progress
    
    // 如果还有下载中的任务，继续监控
    const hasDownloading = progress.some(p => p.status === 'downloading' || p.status === 'pending')
    if (hasDownloading) {
      setTimeout(checkProgress, 1000)
    }
  }
  
  checkProgress()
}

const refreshVersions = async () => {
  const result = await configManager.getAvailableVersions()
  if (result.success) {
    availableVersions.value = result.versions || []
    currentVersion.value = result.currentVersion || null
  }
}

const switchVersion = async () => {
  if (selectedVersion.value) {
    const success = await configManager.switchToVersion(selectedVersion.value)
    if (success) {
      await updateStatus()
    }
  }
}

const cleanupOldVersions = async () => {
  await configManager.cleanupOldVersions(3)
  await refreshVersions()
}

const testConfigValue = () => {
  const value = configManager.getConfigValue(testKeyPath.value)
  testResult.value = JSON.stringify(value, null, 2)
}

const updateStatus = async () => {
  currentVersion.value = configManager.getCurrentVersion()
  configLoaded.value = !!configManager.getCurrentConfig()
  await refreshVersions()
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'completed': return 'status-success'
    case 'error': return 'status-error'
    case 'downloading': return 'status-info'
    default: return 'status-default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '等待中'
    case 'downloading': return '下载中'
    case 'completed': return '已完成'
    case 'error': return '失败'
    default: return status
  }
}

// 生命周期
onMounted(async () => {
  await configManager.initialize()
  await updateStatus()
})
</script>

<style scoped>
.config-manager {
  padding: 20px;
}

.section {
  margin-bottom: 30px;
}

.section h3 {
  margin-bottom: 15px;
  color: #409eff;
}

.progress-item {
  margin-bottom: 15px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.status-success { color: #67c23a; }
.status-error { color: #f56c6c; }
.status-info { color: #409eff; }
.status-default { color: #909399; }

.error-text {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 5px;
}

.version-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.config-preview {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.config-item {
  margin-bottom: 20px;
}

.preview-image {
  max-width: 200px;
  max-height: 100px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin: 5px 0;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.button-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.preview-button {
  width: 50px;
  height: 50px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.test-result {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  white-space: pre-wrap;
  font-family: monospace;
}
</style>
