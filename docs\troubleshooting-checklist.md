# Electron API 故障排除检查清单

## `electronApi is not defined` 错误解决步骤

### ✅ 检查清单

#### 1. 导入语句检查
- [ ] 确认文件顶部有正确的导入语句
```javascript
import electronApi from '@/utils/electronApi'
```

- [ ] 检查导入路径是否正确
- [ ] 确认没有拼写错误

#### 2. 文件路径检查
- [ ] 确认 `src/renderer/src/utils/electronApi.js` 文件存在
- [ ] 检查路径别名 `@` 是否正确配置
- [ ] 验证 Vite/Webpack 配置中的别名设置

#### 3. 语法检查
- [ ] 确认使用的是 ES6 模块语法（`import/export`）
- [ ] 检查是否在正确的作用域中使用 `electronApi`
- [ ] 确认没有在导入之前使用 `electronApi`

#### 4. 环境检查
- [ ] 确认项目正在运行（`npm run dev`）
- [ ] 检查浏览器控制台是否有其他错误
- [ ] 验证模块加载器是否正常工作

### 🔧 快速修复步骤

#### 步骤 1: 验证文件存在
```bash
# 检查文件是否存在
ls -la src/renderer/src/utils/electronApi.js
```

#### 步骤 2: 测试导入
在浏览器控制台中运行：
```javascript
// 测试动态导入
import('@/utils/electronApi')
  .then(module => console.log('✅ 导入成功:', module.default))
  .catch(error => console.error('❌ 导入失败:', error))
```

#### 步骤 3: 检查具体文件
对于报错的文件，确保有正确的导入：

**Vue 组件示例:**
```vue
<script>
import electronApi from '@/utils/electronApi'

export default {
  data() {
    return {
      isTest: electronApi.globals.getIsTest()
    }
  }
}
</script>
```

**JavaScript 文件示例:**
```javascript
import electronApi from '@/utils/electronApi'

// 使用 API
const config = await electronApi.config.getConfig()
```

#### 步骤 4: 常见错误修复

**错误 1: 路径问题**
```javascript
// ❌ 错误
import electronApi from './electronApi'
import electronApi from '../utils/electronApi'

// ✅ 正确
import electronApi from '@/utils/electronApi'
```

**错误 2: 作用域问题**
```javascript
// ❌ 错误 - 在导入之前使用
console.log(electronApi.isElectron())
import electronApi from '@/utils/electronApi'

// ✅ 正确 - 先导入再使用
import electronApi from '@/utils/electronApi'
console.log(electronApi.isElectron())
```

**错误 3: 异步使用问题**
```javascript
// ❌ 错误 - 在异步函数中直接使用
setTimeout(() => {
  electronApi.app.closeApp() // 可能报错
}, 1000)

// ✅ 正确 - 确保在正确的作用域中
import electronApi from '@/utils/electronApi'
setTimeout(() => {
  electronApi.app.closeApp()
}, 1000)
```

### 🚨 紧急修复

如果上述步骤都无法解决问题，尝试以下紧急修复：

#### 方法 1: 重新创建导入
删除现有的导入语句，重新输入：
```javascript
import electronApi from '@/utils/electronApi'
```

#### 方法 2: 使用动态导入
```javascript
// 在需要使用的地方
const electronApi = await import('@/utils/electronApi')
electronApi.default.app.closeApp()
```

#### 方法 3: 检查文件编码
确保文件使用 UTF-8 编码，没有 BOM 标记。

### 📋 验证修复

修复后，运行以下测试确认问题已解决：

```javascript
// 在浏览器控制台中运行
import { testElectronApi } from '@/utils/electronApiTest'
testElectronApi().then(success => {
  if (success) {
    console.log('✅ 修复成功！')
  } else {
    console.log('❌ 仍有问题，请检查控制台错误')
  }
})
```

### 📞 获取帮助

如果问题仍然存在：

1. 检查浏览器控制台的完整错误信息
2. 确认项目的构建配置
3. 查看 `docs/electron-api-integration.md` 获取更多信息
4. 运行 `npm run dev` 确保开发服务器正常运行

### 🔍 常见场景

#### 场景 1: Vue 组件中使用
```vue
<template>
  <div>{{ isElectron ? 'Electron' : '浏览器' }}</div>
</template>

<script>
import electronApi from '@/utils/electronApi'

export default {
  data() {
    return {
      isElectron: electronApi.isElectron()
    }
  }
}
</script>
```

#### 场景 2: 工具函数中使用
```javascript
// utils/helper.js
import electronApi from '@/utils/electronApi'

export const getAppInfo = async () => {
  const config = await electronApi.config.getConfig()
  const machineInfo = await electronApi.device.getMachineInfo()
  return { config, machineInfo }
}
```

#### 场景 3: Store 中使用
```javascript
// store/config.js
import electronApi from '@/utils/electronApi'

export const useConfigStore = defineStore('config', {
  state: () => ({
    isTest: electronApi.globals.getIsTest()
  }),
  actions: {
    async loadConfig() {
      this.config = await electronApi.config.getConfig()
    }
  }
})
```

记住：每个使用 `electronApi` 的文件都必须有正确的导入语句！
