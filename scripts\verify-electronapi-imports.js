/**
 * 验证 electronApi 导入的脚本
 * 检查所有使用 electronApi 的文件是否都有正确的导入语句
 */

const fs = require('fs')
const path = require('path')

// 需要检查的目录
const checkDirs = [
  'src/renderer/src/views',
  'src/renderer/src/components',
  'src/renderer/src/utils',
  'src/renderer/src/store'
]

// 排除的文件
const excludeFiles = [
  'electronApi.js',
  'electronApiTest.js'
]

// 递归获取所有 .vue 和 .js/.ts 文件
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      getAllFiles(filePath, fileList)
    } else if (
      (file.endsWith('.vue') || file.endsWith('.js') || file.endsWith('.ts')) &&
      !excludeFiles.includes(file)
    ) {
      fileList.push(filePath)
    }
  })
  
  return fileList
}

// 检查文件内容
function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8')
  const hasElectronApiUsage = content.includes('electronApi.')
  const hasImport = content.includes("import electronApi from '@/utils/electronApi'")
  
  return {
    filePath,
    hasElectronApiUsage,
    hasImport,
    needsImport: hasElectronApiUsage && !hasImport
  }
}

// 主函数
function main() {
  console.log('🔍 开始验证 electronApi 导入...\n')
  
  let allFiles = []
  
  // 收集所有文件
  checkDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      getAllFiles(dir, allFiles)
    }
  })
  
  console.log(`📁 总共检查 ${allFiles.length} 个文件\n`)
  
  const results = allFiles.map(checkFile)
  const problemFiles = results.filter(result => result.needsImport)
  
  if (problemFiles.length === 0) {
    console.log('✅ 所有文件都正确导入了 electronApi！')
  } else {
    console.log(`❌ 发现 ${problemFiles.length} 个文件需要添加 electronApi 导入：\n`)
    
    problemFiles.forEach(file => {
      console.log(`📄 ${file.filePath}`)
      console.log('   需要添加: import electronApi from \'@/utils/electronApi\'\n')
    })
    
    console.log('🔧 修复建议：')
    console.log('在每个问题文件的 <script> 标签开头添加导入语句：')
    console.log("import electronApi from '@/utils/electronApi'\n")
  }
  
  // 统计信息
  const usageFiles = results.filter(result => result.hasElectronApiUsage)
  const importFiles = results.filter(result => result.hasImport)
  
  console.log('📊 统计信息：')
  console.log(`- 使用 electronApi 的文件: ${usageFiles.length}`)
  console.log(`- 已正确导入的文件: ${importFiles.length}`)
  console.log(`- 需要修复的文件: ${problemFiles.length}`)
  
  return problemFiles.length === 0
}

// 运行检查
if (require.main === module) {
  const success = main()
  process.exit(success ? 0 : 1)
}

module.exports = { main, checkFile, getAllFiles }
