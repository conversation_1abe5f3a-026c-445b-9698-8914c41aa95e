<template>
  <div class="config-update-checker">
    <el-card header="配置更新检查">
      <div class="status-section">
        <h3>当前状态</h3>
        <p><strong>本地版本:</strong> {{ localVersion || '未安装' }}</p>
        <p><strong>最新版本:</strong> {{ latestVersion || '检查中...' }}</p>
        <p><strong>更新状态:</strong> 
          <el-tag :type="updateStatusType">{{ updateStatusText }}</el-tag>
        </p>
      </div>

      <div class="actions-section">
        <el-button 
          type="primary" 
          @click="checkUpdates"
          :loading="checking"
          icon="Refresh"
        >
          检查更新
        </el-button>
        
        <el-button 
          v-if="hasUpdate"
          type="success" 
          @click="performUpdate"
          :loading="updating"
          icon="Download"
        >
          立即更新
        </el-button>

        <el-button 
          type="info" 
          @click="showVersionHistory"
          icon="List"
        >
          版本历史
        </el-button>
      </div>

      <div v-if="updateProgress.length > 0" class="progress-section">
        <h3>更新进度</h3>
        <div v-for="progress in updateProgress" :key="progress.url" class="progress-item">
          <div class="progress-header">
            <span>{{ progress.filename }}</span>
            <span :class="getStatusClass(progress.status)">{{ getStatusText(progress.status) }}</span>
          </div>
          <el-progress 
            :percentage="progress.progress" 
            :status="progress.status === 'error' ? 'exception' : progress.status === 'completed' ? 'success' : undefined"
          />
        </div>
      </div>

      <div v-if="versionHistory.length > 0" class="history-section">
        <h3>版本历史</h3>
        <el-table :data="versionHistory" style="width: 100%">
          <el-table-column prop="version" label="版本" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.current ? 'success' : 'info'">
                {{ scope.row.current ? '当前' : '已安装' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button 
                v-if="!scope.row.current"
                size="small" 
                @click="switchToVersion(scope.row.version)"
              >
                切换到此版本
              </el-button>
              <span v-else class="current-version-text">当前使用版本</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import configManager from '../utils/configManager'
import { checkForUpdates, updateConfig } from '../utils/configHelper'
import type { DownloadProgress } from '../utils/configManager'

// 响应式数据
const localVersion = ref<string | null>(null)
const latestVersion = ref<string | null>(null)
const hasUpdate = ref(false)
const checking = ref(false)
const updating = ref(false)
const updateProgress = ref<DownloadProgress[]>([])
const versionHistory = ref<Array<{version: string, current: boolean}>>([])

// 计算属性
const updateStatusType = computed(() => {
  if (hasUpdate.value) return 'warning'
  if (localVersion.value && latestVersion.value) return 'success'
  return 'info'
})

const updateStatusText = computed(() => {
  if (checking.value) return '检查中...'
  if (hasUpdate.value) return '有新版本可用'
  if (localVersion.value && latestVersion.value) return '已是最新版本'
  return '未知'
})

// 方法
const checkUpdates = async () => {
  checking.value = true
  
  try {
    const result = await checkForUpdates()
    
    localVersion.value = result.localVersion
    latestVersion.value = result.apiVersion
    hasUpdate.value = result.hasUpdate
    
    if (result.hasUpdate) {
      ElMessage.info(`发现新版本 ${result.apiVersion}`)
    } else {
      ElMessage.success('当前已是最新版本')
    }
  } catch (error) {
    console.error('检查更新失败:', error)
    ElMessage.error('检查更新失败')
  } finally {
    checking.value = false
  }
}

const performUpdate = async () => {
  updating.value = true
  
  try {
    // 开始监控下载进度
    startProgressMonitoring()
    
    const result = await updateConfig()
    
    if (result.success) {
      ElMessage.success(result.message)
      localVersion.value = result.newVersion || localVersion.value
      hasUpdate.value = false
      await loadVersionHistory()
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error('更新失败')
  } finally {
    updating.value = false
    updateProgress.value = []
  }
}

const startProgressMonitoring = async () => {
  const checkProgress = async () => {
    const progress = await configManager.getDownloadProgress()
    updateProgress.value = progress
    
    // 如果还有下载中的任务，继续监控
    const hasDownloading = progress.some(p => p.status === 'downloading' || p.status === 'pending')
    if (hasDownloading && updating.value) {
      setTimeout(checkProgress, 1000)
    }
  }
  
  checkProgress()
}

const loadVersionHistory = async () => {
  try {
    const result = await configManager.getAvailableVersions()
    if (result.success && result.versions) {
      versionHistory.value = result.versions.map(version => ({
        version,
        current: version === result.currentVersion
      })).reverse() // 最新版本在前
    }
  } catch (error) {
    console.error('加载版本历史失败:', error)
  }
}

const showVersionHistory = async () => {
  await loadVersionHistory()
}

const switchToVersion = async (version: string) => {
  try {
    const success = await configManager.switchToVersion(version)
    if (success) {
      localVersion.value = version
      await loadVersionHistory()
    }
  } catch (error) {
    console.error('切换版本失败:', error)
  }
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'completed': return 'status-success'
    case 'error': return 'status-error'
    case 'downloading': return 'status-info'
    default: return 'status-default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '等待中'
    case 'downloading': return '下载中'
    case 'completed': return '已完成'
    case 'error': return '失败'
    default: return status
  }
}

// 生命周期
onMounted(async () => {
  localVersion.value = configManager.getCurrentVersion()
  await checkUpdates()
  await loadVersionHistory()
})
</script>

<style scoped>
.config-update-checker {
  padding: 20px;
}

.status-section,
.actions-section,
.progress-section,
.history-section {
  margin-bottom: 30px;
}

.status-section h3,
.progress-section h3,
.history-section h3 {
  margin-bottom: 15px;
  color: #409eff;
}

.actions-section {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.progress-item {
  margin-bottom: 15px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.status-success { color: #67c23a; }
.status-error { color: #f56c6c; }
.status-info { color: #409eff; }
.status-default { color: #909399; }

.current-version-text {
  color: #67c23a;
  font-weight: bold;
}
</style>
